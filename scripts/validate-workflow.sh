#!/bin/bash

# Tibco BW to Spring Boot 端到端验证脚本
# 这个脚本验证完整的转换工作流程

set -e  # 遇到错误立即退出

echo "🚀 Starting Tibco BW to Spring Boot validation workflow..."

# 1. 清理之前的输出
echo "🧹 Cleaning previous outputs..."
rm -rf temp-output
rm -rf spring-boilerplate/src/main/java/com/example/movies

# 2. 构建 CLI 工具
echo "🔨 Building CLI tool..."
npm run build

# 3. 运行自动转换
echo "🔄 Running auto conversion..."
node dist/cli.js auto test/_fixtures/

# 4. 验证生成的文件
echo "📋 Validating generated files..."
if [ ! -f "spring-boilerplate/src/main/java/com/example/movies/SortMoviesController.java" ]; then
    echo "❌ Controller file not found"
    exit 1
fi

if [ ! -f "spring-boilerplate/src/main/java/com/example/movies/SortMoviesService.java" ]; then
    echo "❌ Service file not found"
    exit 1
fi

# 5. 编译 Spring Boot 项目
echo "🔨 Compiling Spring Boot project..."
cd spring-boilerplate
mvn clean compile -q

# 6. 运行测试
echo "🧪 Running Spring Boot tests..."
mvn test -q

# 7. 验证 toString 方法
echo "🔍 Validating toString methods in generated classes..."
cd ..
if ! grep -r "toString()" spring-boilerplate/src/main/java/com/example/movies/ > /dev/null; then
    echo "❌ toString methods not found in generated classes"
    exit 1
fi

echo "✅ All validations passed!"
echo ""
echo "🎉 Tibco BW to Spring Boot conversion workflow is working correctly!"
echo ""
echo "📋 Summary:"
echo "   - BWP files automatically detected and parsed"
echo "   - XSD schemas automatically processed"
echo "   - Java classes generated with toString methods"
echo "   - Spring Boot project compiled successfully"
echo "   - All tests passed"
echo ""
echo "🚀 Next steps:"
echo "   1. cd spring-boilerplate"
echo "   2. mvn spring-boot:run"
echo "   3. Test endpoints at http://localhost:8080"
