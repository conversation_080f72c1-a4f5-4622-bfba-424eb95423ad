# Tibco BW to Spring Boot CLI

A powerful command-line tool that converts TIBCO BusinessWorks (.bwp) files to Spring Boot Java applications with automatic XSD model generation and deployment capabilities.

## Features

- 🔄 **BWP to Java Conversion**: Convert TIBCO BusinessWorks processes to Spring Boot REST controllers and services
- 🏗️ **XSD Model Generation**: Automatically generate Java POJO classes from XSD schemas
- 🚀 **Spring Boot Deployment**: Deploy generated code directly to existing Spring Boot projects
- ✅ **Validation**: Validate BWP files before conversion
- 🎯 **Customizable**: Support for various Java code generation options (JSR-303, Jackson, Lombok, etc.)
- 📦 **Complete Workflow**: End-to-end conversion from BWP files to running Spring Boot applications

## Installation

```bash
npm install -g tibco-bw-to-springboot-cli
```

Or clone and build locally:

```bash
git clone <repository-url>
cd tibco-movie-example
npm install
npm run build
```

## Quick Start

### 🚀 Simplest Usage - Auto Convert Entire Tibco BW Directory

The easiest way to convert your Tibco BW project:

```bash
# Just specify the Tibco BW directory - everything else is automatic!
node dist/cli.js auto test/_fixtures/

# Or with custom package name
node dist/cli.js auto test/_fixtures/ -p com.mycompany.api

# Skip deployment to Spring Boot project
node dist/cli.js auto test/_fixtures/ --no-deploy
```

### ✨ What the `auto` command does:
- 🔍 **Auto-detects** BWP files in the directory
- 📁 **Auto-finds** Schemas directory
- 📄 **Auto-locates** swagger.json (if available)
- 🏗️ **Generates** Java model classes with toString methods
- 🎯 **Creates** Spring Boot controllers and services
- 🚀 **Deploys** everything to the spring-boilerplate project
- ✅ **Validates** that everything compiles and tests pass

### Alternative - Convert Specific BWP File

For more control, convert a specific BWP file:

```bash
# Quick convert specific BWP file
node dist/cli.js quick path/to/your/process.bwp

# Convert BWP to Java models only
node dist/cli.js convert \
  -i path/to/your/process.bwp \
  -s path/to/schemas/directory \
  -o ./generated \
  -p com.example.movies
```

### Example with Sample Data

Using the included sample BWP file:

```bash
# Build the CLI tool
npm run build

# Convert the sample SearchMovies.bwp file
node dist/cli.js convert \
  -i "test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp" \
  -s "test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas" \
  -o "./demo-output" \
  -p "com.example.movies"
```

This will generate:
- `SearchMoviesController.java` - REST controller with `/movies` endpoint
- `SearchMoviesService.java` - Service layer with business logic
- 30+ model classes generated from XSD schemas
- Complete Spring Boot-ready code structure

## Command Reference

### `convert` Command

Convert a BWP file to Spring Boot Java code.

**Options:**
- `-i, --input <path>` - Path to the BWP file (required)
- `-o, --output <path>` - Output directory for generated Java code (default: ./generated)
- `-p, --package <name>` - Java package name (default: com.example.app)
- `-s, --schemas <path>` - Path to XSD schemas directory
- `--spring-boot-project <path>` - Path to Spring Boot project for deployment
- `--no-validation` - Disable JSR-303 validation annotations
- `--lombok` - Use Lombok annotations
- `--no-jackson` - Disable Jackson annotations
- `--no-constructors` - Skip constructor generation
- `--no-tostring` - Skip toString method generation

### `validate` Command

Validate a BWP file without generating code.

**Options:**
- `-i, --input <path>` - Path to the BWP file (required)

### `generate-models` Command

Generate Java model classes from XSD schemas.

**Options:**
- `-s, --schemas <path>` - Path to XSD schemas directory (required)
- `-o, --output <path>` - Output directory for generated Java code (default: ./generated)
- `-p, --package <name>` - Java package name (default: com.example.app.model)

## Generated Code Structure

The tool generates the following structure:

```
output/
└── com/
    └── example/
        └── movies/
            ├── SearchMoviesController.java  # REST Controller
            ├── SearchMoviesService.java     # Business Logic Service
            ├── MoviesGetParameters.java     # Input Model
            ├── OMDBSearchElement.java       # Output Model
            └── ... (other XSD-generated models)
```

### Generated Controller Features

- Spring Boot REST Controller with `@RestController`
- Proper HTTP method mappings (`@GetMapping`, `@PostMapping`, etc.)
- Request parameter binding
- Response entity handling
- Error handling with appropriate HTTP status codes

### Generated Service Features

- Spring Service with `@Service` annotation
- RestTemplate integration for external API calls
- Business logic implementation
- Exception handling

### Generated Model Features

- Jackson annotations for JSON serialization/deserialization
- JSR-303 validation annotations (optional)
- Lombok support (optional)
- Proper constructors and toString methods
- Type-safe field mappings from XSD schemas

## Development

### Building from Source

```bash
git clone <repository-url>
cd tibco-movie-example
npm install
npm run build
```

### Running Tests

```bash
npm test
```

### Running Integration Tests

```bash
npm test -- test/integration/
```

## Architecture

The tool consists of several key components:

- **BWP Parser**: Parses TIBCO BusinessWorks .bwp files and extracts process information
- **XSD Parser**: Processes XSD schema files and generates Java model classes
- **Java Code Generator**: Generates Spring Boot controllers and services
- **Spring Boot Deployer**: Deploys generated code to existing Spring Boot projects
- **CLI Interface**: Command-line interface for easy usage

## Requirements

- Node.js 14+
- TypeScript 4+
- Java 8+ (for generated code)
- Spring Boot 2.7+ (for deployment target)

## License

MIT License - see LICENSE file for details.


