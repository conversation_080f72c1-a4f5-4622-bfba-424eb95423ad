package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Root Element
 */
@JsonRootName("StatusLine")
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatusLine {

    @NotNull
    @JsonProperty("statusCode")
    private Integer statusCode;

    /**
     * Default constructor
     */
    public StatusLine() {
    }

    /**
     * Parameterized constructor
     */
    public StatusLine(Integer statusCode) {
        this.statusCode = statusCode;
    }

    /**
     * Get statusCode
     */
    public Integer getStatusCode() {
        return statusCode;
    }

    /**
     * Set statusCode
     */
    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    public String toString() {
        return "StatusLine{" +
                "statusCode=" + statusCode +
                "}";
    }
}
