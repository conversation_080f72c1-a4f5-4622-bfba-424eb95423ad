package com.example.movies.model;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://www.example.org/MovieCatalogMaster
 * Complex Type
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class MoviesType {

    @Valid
    @JsonProperty("movieDetail")
    private Details movieDetail;

    /**
     * Default constructor
     */
    public MoviesType() {
    }

    /**
     * Parameterized constructor
     */
    public MoviesType(Details movieDetail) {
        this.movieDetail = movieDetail;
    }

    /**
     * Get movieDetail
     */
    public Details getMovieDetail() {
        return movieDetail;
    }

    /**
     * Set movieDetail
     */
    public void setMovieDetail(Details movieDetail) {
        this.movieDetail = movieDetail;
    }

    @Override
    public String toString() {
        return "MoviesType{" +
                "movieDetail=" + movieDetail +
                "}";
    }
}
