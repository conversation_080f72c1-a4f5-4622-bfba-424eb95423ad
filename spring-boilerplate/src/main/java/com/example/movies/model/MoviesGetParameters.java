package com.example.movies.model;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://xmlns.example.com/SearchMovies/parameters
 * Root Element
 */
@JsonRootName("MoviesGetParameters")
@JsonIgnoreProperties(ignoreUnknown = true)
public class MoviesGetParameters {

    @NotNull
    @NotBlank
    @JsonProperty("searchString")
    private String searchString;

    /**
     * Default constructor
     */
    public MoviesGetParameters() {
    }

    /**
     * Parameterized constructor
     */
    public MoviesGetParameters(String searchString) {
        this.searchString = searchString;
    }

    /**
     * Get searchString
     */
    public String getSearchString() {
        return searchString;
    }

    /**
     * Set searchString
     */
    public void setSearchString(String searchString) {
        this.searchString = searchString;
    }

    @Override
    public String toString() {
        return "MoviesGetParameters{" +
                "searchString=" + searchString +
                "}";
    }
}
