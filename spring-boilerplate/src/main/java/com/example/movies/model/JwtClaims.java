package com.example.movies.model;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Root Element
 */
@JsonRootName("JwtClaims")
@JsonIgnoreProperties(ignoreUnknown = true)
public class JwtClaims {

    @Valid
    @JsonProperty("claim")
    private List<JwtClaimElementType> claim = new ArrayList<>();

    @JsonProperty("payload")
    private String payload;

    /**
     * Default constructor
     */
    public JwtClaims() {
    }

    /**
     * Parameterized constructor
     */
    public JwtClaims(List<JwtClaimElementType> claim, String payload) {
        this.claim = claim;
        this.payload = payload;
    }

    /**
     * Get claim
     */
    public List<JwtClaimElementType> getClaim() {
        return claim;
    }

    /**
     * Set claim
     */
    public void setClaim(List<JwtClaimElementType> claim) {
        this.claim = claim;
    }


    /**
     * Get payload
     */
    public String getPayload() {
        return payload;
    }

    /**
     * Set payload
     */
    public void setPayload(String payload) {
        this.payload = payload;
    }

    @Override
    public String toString() {
        return "JwtClaims{" +
                "claim=" + claim + ", " + "payload=" + payload +
                "}";
    }
}
