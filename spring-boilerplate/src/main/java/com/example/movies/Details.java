package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://www.example.org/MovieCatalogMaster
 * Complex Type
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Details {

    @JsonProperty("imdbID")
    private String imdbID;

    @JsonProperty("Title")
    private String title;

    @JsonProperty("Year")
    private String year;

    @JsonProperty("Plot")
    private String plot;

    @JsonProperty("Type")
    private String type;

    /**
     * Default constructor
     */
    public Details() {
    }

    /**
     * Parameterized constructor
     */
    public Details(String imdbID, String title, String year, String plot, String type) {
        this.imdbID = imdbID;
        this.title = title;
        this.year = year;
        this.plot = plot;
        this.type = type;
    }

    /**
     * Get imdbID
     */
    public String getImdbID() {
        return imdbID;
    }

    /**
     * Set imdbID
     */
    public void setImdbID(String imdbID) {
        this.imdbID = imdbID;
    }


    /**
     * Get title
     */
    public String getTitle() {
        return title;
    }

    /**
     * Set title
     */
    public void setTitle(String title) {
        this.title = title;
    }


    /**
     * Get year
     */
    public String getYear() {
        return year;
    }

    /**
     * Set year
     */
    public void setYear(String year) {
        this.year = year;
    }


    /**
     * Get plot
     */
    public String getPlot() {
        return plot;
    }

    /**
     * Set plot
     */
    public void setPlot(String plot) {
        this.plot = plot;
    }


    /**
     * Get type
     */
    public String getType() {
        return type;
    }

    /**
     * Set type
     */
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "Details{" +
                "imdbID=" + imdbID + ", " + "title=" + title + ", " + "year=" + year + ", " + "plot=" + plot + ", " + "type=" + type +
                "}";
    }
}
