package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Complex Type
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DynamicHeadersType {

    @Valid
    @JsonProperty("Header")
    private List<DynamicHeadersTypeDetails> header = new ArrayList<>();

    /**
     * Default constructor
     */
    public DynamicHeadersType() {
    }

    /**
     * Parameterized constructor
     */
    public DynamicHeadersType(List<DynamicHeadersTypeDetails> header) {
        this.header = header;
    }

    /**
     * Get header
     */
    public List<DynamicHeadersTypeDetails> getHeader() {
        return header;
    }

    /**
     * Set header
     */
    public void setHeader(List<DynamicHeadersTypeDetails> header) {
        this.header = header;
    }

    @Override
    public String toString() {
        return "DynamicHeadersType{" +
                "header=" + header +
                "}";
    }
}
