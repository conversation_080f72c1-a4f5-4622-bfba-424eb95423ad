spring.application.name=movies-api

# Server Configuration
server.port=8080
server.servlet.context-path=/

# OMDB API Configuration
omdb.api.url=http://www.omdbapi.com/
omdb.api.key=62eec860

# Search Service Configuration (external service)
search.service.host=localhost
search.service.port=8080
search.service.url=http://${search.service.host}:${search.service.port}

# Details Service Configuration (OMDB API)
details.service.host=www.omdbapi.com
details.service.port=80
details.service.url=http://${details.service.host}

# Logging Configuration
logging.level.com.example.movies=DEBUG
logging.level.org.springframework.web=INFO

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
