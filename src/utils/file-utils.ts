import * as fs from 'fs';
import * as path from 'path';

/**
 * 确保目录存在，如果不存在则创建
 */
export function ensureDirectoryExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * 读取目录中的所有 XSD 文件
 */
export function findXSDFiles(directory: string, maxDepth: number = 10, currentDepth: number = 0): string[] {
  const xsdFiles: string[] = [];

  if (!fs.existsSync(directory)) {
    throw new Error(`Directory does not exist: ${directory}`);
  }

  if (currentDepth >= maxDepth) {
    console.warn(`Maximum recursion depth reached for directory: ${directory}`);
    return xsdFiles;
  }

  const files = fs.readdirSync(directory);

  for (const file of files) {
    const filePath = path.join(directory, file);

    try {
      const stat = fs.statSync(filePath);

      if (stat.isFile() && file.toLowerCase().endsWith('.xsd')) {
        xsdFiles.push(filePath);
      } else if (stat.isDirectory()) {
        // 递归搜索子目录，增加深度计数
        xsdFiles.push(...findXSDFiles(filePath, maxDepth, currentDepth + 1));
      }
    } catch (error) {
      console.warn(`Error accessing file ${filePath}:`, error);
      continue;
    }
  }

  return xsdFiles;
}

/**
 * 读取文件内容
 */
export function readFileContent(filePath: string): string {
  if (!fs.existsSync(filePath)) {
    throw new Error(`File does not exist: ${filePath}`);
  }
  
  return fs.readFileSync(filePath, 'utf-8');
}

/**
 * 写入文件内容
 */
export function writeFileContent(filePath: string, content: string): void {
  const directory = path.dirname(filePath);
  ensureDirectoryExists(directory);
  fs.writeFileSync(filePath, content, 'utf-8');
}

/**
 * 获取相对于某个基础目录的相对路径
 */
export function getRelativePath(from: string, to: string): string {
  return path.relative(from, to);
}

/**
 * 规范化路径
 */
export function normalizePath(filePath: string): string {
  return path.normalize(filePath);
}

/**
 * 获取文件名（不包含扩展名）
 */
export function getFileNameWithoutExtension(filePath: string): string {
  const basename = path.basename(filePath);
  const extname = path.extname(basename);
  return basename.slice(0, -extname.length);
}

/**
 * 检查文件是否存在
 */
export function fileExists(filePath: string): boolean {
  return fs.existsSync(filePath);
}
