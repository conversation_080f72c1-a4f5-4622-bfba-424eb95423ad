import { ParsedRestEndpoint, ParsedRestOperationParameter } from '../types/index';

/**
 * 参数映射器
 * 处理 Tibco BW 参数到实际外部 API 参数的映射
 */
export class ParameterMapper {
  
  /**
   * 映射外部 API 参数
   */
  static mapExternalApiParameters(endpoint: ParsedRestEndpoint): ParsedRestOperationParameter[] {
    // 检查是否为 OMDB API
    if (this.isOmdbApi(endpoint)) {
      return this.mapOmdbParameters(endpoint);
    }
    
    // 其他外部 API 的通用映射
    return this.mapGenericParameters(endpoint);
  }

  /**
   * 检查是否为 OMDB API
   */
  private static isOmdbApi(endpoint: ParsedRestEndpoint): boolean {
    return (endpoint.baseUrl?.includes('omdbapi.com') ?? false) ||
           (endpoint.connector?.includes('HttpClientResource1') ?? false);
  }

  /**
   * 映射 OMDB API 参数
   */
  private static mapOmdbParameters(endpoint: ParsedRestEndpoint): ParsedRestOperationParameter[] {
    const mappedParams: ParsedRestOperationParameter[] = [];
    
    for (const param of endpoint.parameters) {
      const mappedParam = this.mapOmdbParameter(param);
      if (mappedParam) {
        mappedParams.push(mappedParam);
      }
    }
    
    // 确保总是包含 apikey 参数
    if (!mappedParams.some(p => p.name === 'apikey')) {
      mappedParams.push({
        name: 'apikey',
        dataType: 'string',
        parameterType: 'Query',
        required: true
      });
    }
    
    return mappedParams;
  }

  /**
   * 映射单个 OMDB 参数
   */
  private static mapOmdbParameter(param: ParsedRestOperationParameter): ParsedRestOperationParameter | null {
    switch (param.name.toLowerCase()) {
      case 'searchstring':
      case 'search':
      case 'query':
        // 搜索参数映射到 's'
        return {
          ...param,
          name: 's',
          originalName: param.name
        };
      
      case 'i':
      case 'imdbid':
      case 'id':
        // ID 参数保持为 'i'
        return {
          ...param,
          name: 'i',
          originalName: param.name
        };
      
      case 'apikey':
      case 'key':
        // API key 参数
        return {
          ...param,
          name: 'apikey',
          originalName: param.name
        };
      
      case 'type':
        // 类型参数（movie, series, episode）
        return {
          ...param,
          name: 'type',
          originalName: param.name
        };
      
      case 'year':
      case 'y':
        // 年份参数
        return {
          ...param,
          name: 'y',
          originalName: param.name
        };
      
      case 'plot':
        // 情节长度参数（short, full）
        return {
          ...param,
          name: 'plot',
          originalName: param.name
        };
      
      case 'callback':
        // JSONP 回调参数
        return {
          ...param,
          name: 'callback',
          originalName: param.name
        };
      
      default:
        // 未知参数，保持原样但添加警告
        console.warn(`Unknown OMDB parameter: ${param.name}, keeping as-is`);
        return {
          ...param,
          originalName: param.name
        };
    }
  }

  /**
   * 映射通用外部 API 参数
   */
  private static mapGenericParameters(endpoint: ParsedRestEndpoint): ParsedRestOperationParameter[] {
    // 对于通用 API，保持参数名不变，但添加 originalName 字段
    return endpoint.parameters.map(param => ({
      ...param,
      originalName: param.name
    }));
  }

  /**
   * 生成参数映射代码
   */
  static generateParameterMappingCode(
    originalParams: ParsedRestOperationParameter[], 
    mappedParams: ParsedRestOperationParameter[]
  ): string {
    const mappings: string[] = [];
    
    for (const mappedParam of mappedParams) {
      const originalParam = originalParams.find(p => 
        p.name === mappedParam.originalName || p.name === mappedParam.name
      );
      
      if (originalParam) {
        const originalName = this.toCamelCase(originalParam.name);
        const mappedName = mappedParam.name;
        
        if (originalParam.name !== mappedParam.name) {
          // 参数名发生了映射
          mappings.push(`"${mappedName}=" + ${originalName}`);
        } else {
          // 参数名没有变化
          mappings.push(`"${mappedName}=" + ${originalName}`);
        }
      } else if (mappedParam.name === 'apikey') {
        // 特殊处理 API key
        mappings.push(`"apikey=" + apiKey`);
      }
    }
    
    return mappings.join(' + "&" + ');
  }

  /**
   * 检测 API 调用类型
   */
  static detectApiCallType(endpoint: ParsedRestEndpoint): 'search' | 'details' | 'generic' {
    if (this.isOmdbApi(endpoint)) {
      // 检查参数来确定是搜索还是详情调用
      const hasSearchParam = endpoint.parameters.some(p => 
        ['searchstring', 'search', 'query', 's'].includes(p.name.toLowerCase())
      );
      const hasIdParam = endpoint.parameters.some(p => 
        ['i', 'imdbid', 'id'].includes(p.name.toLowerCase())
      );
      
      if (hasSearchParam) {
        return 'search';
      } else if (hasIdParam) {
        return 'details';
      }
    }
    
    return 'generic';
  }

  /**
   * 转换为驼峰命名
   */
  private static toCamelCase(str: string): string {
    return str.replace(/[-_](.)/g, (_, char) => char.toUpperCase())
              .replace(/^./, char => char.toLowerCase());
  }
}

/**
 * 扩展参数接口以支持原始名称
 */
declare module '../types/index' {
  interface ParsedRestOperationParameter {
    originalName?: string;
  }
}
