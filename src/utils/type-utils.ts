/**
 * XSD 类型到 Java 类型的映射
 */
export const XSD_TO_JAVA_TYPE_MAP: Record<string, string> = {
  'string': 'String',
  'xs:string': 'String',
  'int': 'Integer',
  'xs:int': 'Integer',
  'integer': 'Integer',
  'xs:integer': 'Integer',
  'long': 'Long',
  'xs:long': 'Long',
  'double': 'Double',
  'xs:double': 'Double',
  'float': 'Float',
  'xs:float': 'Float',
  'boolean': 'Boolean',
  'xs:boolean': 'Boolean',
  'date': 'LocalDate',
  'xs:date': 'LocalDate',
  'dateTime': 'LocalDateTime',
  'xs:dateTime': 'LocalDateTime',
  'time': 'LocalTime',
  'xs:time': 'LocalTime',
  'decimal': 'BigDecimal',
  'xs:decimal': 'BigDecimal',
};

/**
 * 将 XSD 类型转换为 Java 类型
 */
export function xsdTypeToJavaType(xsdType: string): string {
  // 处理自定义命名空间类型
  if (xsdType.includes(':')) {
    const parts = xsdType.split(':');
    if (parts.length === 2 && parts[0] !== 'xs') {
      // 自定义类型，返回类名部分
      return capitalizeFirstLetter(parts[1]);
    }
  }
  
  return XSD_TO_JAVA_TYPE_MAP[xsdType] || capitalizeFirstLetter(xsdType);
}

/**
 * 首字母大写
 */
export function capitalizeFirstLetter(str: string): string {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * 首字母小写（用于字段名）
 */
export function lowercaseFirstLetter(str: string): string {
  if (!str) return str;
  return str.charAt(0).toLowerCase() + str.slice(1);
}

/**
 * 转换为驼峰命名
 */
export function toCamelCase(str: string): string {
  return str.replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '');
}

/**
 * 转换为帕斯卡命名（类名）
 */
export function toPascalCase(str: string): string {
  return capitalizeFirstLetter(toCamelCase(str));
}

/**
 * 判断是否为可选字段
 */
export function isOptionalField(minOccurs?: string): boolean {
  return minOccurs === '0' || !minOccurs;
}

/**
 * 判断是否为数组字段
 */
export function isArrayField(maxOccurs?: string): boolean {
  if (!maxOccurs) return false;
  return maxOccurs === 'unbounded' || parseInt(maxOccurs) > 1;
}

/**
 * 提取命名空间前缀
 */
export function extractNamespacePrefix(targetNamespace: string): string {
  const parts = targetNamespace.split('/');
  const lastPart = parts[parts.length - 1];
  return lastPart.toLowerCase().replace(/[^a-zA-Z0-9]/g, '');
}

/**
 * 生成有效的 Java 包名
 */
export function generatePackageName(basePackage: string, namespace?: string): string {
  if (!namespace) return basePackage;
  
  const namespacePart = extractNamespacePrefix(namespace);
  return `${basePackage}.${namespacePart}`;
}

/**
 * 验证 Java 标识符
 */
export function isValidJavaIdentifier(name: string): boolean {
  if (!name || name.length === 0) return false;
  
  // Java 关键字
  const javaKeywords = [
    'abstract', 'assert', 'boolean', 'break', 'byte', 'case', 'catch', 'char',
    'class', 'const', 'continue', 'default', 'do', 'double', 'else', 'enum',
    'extends', 'final', 'finally', 'float', 'for', 'goto', 'if', 'implements',
    'import', 'instanceof', 'int', 'interface', 'long', 'native', 'new',
    'package', 'private', 'protected', 'public', 'return', 'short', 'static',
    'strictfp', 'super', 'switch', 'synchronized', 'this', 'throw', 'throws',
    'transient', 'try', 'void', 'volatile', 'while'
  ];
  
  if (javaKeywords.includes(name.toLowerCase())) {
    return false;
  }
  
  // 检查是否以字母或下划线开头，后续字符为字母、数字或下划线
  return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name);
}

/**
 * 生成安全的 Java 字段名
 */
export function generateSafeFieldName(name: string): string {
  let safeName = toCamelCase(name);
  
  if (!isValidJavaIdentifier(safeName)) {
    // 如果是关键字，添加下划线前缀
    safeName = `_${safeName}`;
  }
  
  return lowercaseFirstLetter(safeName);
}

/**
 * 生成安全的 Java 类名
 */
export function generateSafeClassName(name: string): string {
  let safeName = toPascalCase(name);
  
  if (!isValidJavaIdentifier(safeName)) {
    // 如果是关键字，添加下划线前缀
    safeName = `_${safeName}`;
  }
  
  return safeName;
}
