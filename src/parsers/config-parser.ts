import * as xml2js from 'xml2js';
import * as fs from 'fs';
import * as path from 'path';
import { readFileContent } from '../utils/file-utils';

/**
 * HTTP 客户端资源配置
 */
export interface HttpClientConfig {
  name: string;
  host?: string;
  port?: number;
  substitutionBindings: {
    template: string;
    propName: string;
  }[];
}

/**
 * 全局变量配置
 */
export interface GlobalVariable {
  name: string;
  value: string;
  type: string;
}

/**
 * 解析后的配置信息
 */
export interface ParsedConfiguration {
  httpClients: Map<string, HttpClientConfig>;
  globalVariables: Map<string, GlobalVariable>;
  serviceUrls: Map<string, string>;
}

/**
 * Tibco BW 配置文件解析器
 * 解析 .httpClientResource 和 default.substvar 文件
 */
export class ConfigParser {
  
  /**
   * 解析项目配置
   */
  async parseProjectConfig(projectPath: string): Promise<ParsedConfiguration> {
    const config: ParsedConfiguration = {
      httpClients: new Map(),
      globalVariables: new Map(),
      serviceUrls: new Map()
    };

    // 解析 HTTP 客户端资源
    await this.parseHttpClientResources(projectPath, config);
    
    // 解析全局变量
    await this.parseGlobalVariables(projectPath, config);
    
    // 构建服务 URL 映射
    this.buildServiceUrls(config);

    return config;
  }

  /**
   * 解析 HTTP 客户端资源文件
   */
  private async parseHttpClientResources(projectPath: string, config: ParsedConfiguration): Promise<void> {
    const resourcesPath = path.join(projectPath, 'Resources');
    
    if (!fs.existsSync(resourcesPath)) {
      return;
    }

    // 递归查找所有 .httpClientResource 文件
    const httpClientFiles = this.findFilesWithExtension(resourcesPath, '.httpClientResource');
    
    for (const filePath of httpClientFiles) {
      try {
        const httpClientConfig = await this.parseHttpClientResource(filePath);
        if (httpClientConfig) {
          config.httpClients.set(httpClientConfig.name, httpClientConfig);
        }
      } catch (error) {
        console.warn(`Failed to parse HTTP client resource: ${filePath}`, error);
      }
    }
  }

  /**
   * 解析单个 HTTP 客户端资源文件
   */
  private async parseHttpClientResource(filePath: string): Promise<HttpClientConfig | null> {
    const content = readFileContent(filePath);
    
    return new Promise((resolve, reject) => {
      xml2js.parseString(content, {
        explicitArray: false,
        mergeAttrs: false,
        explicitRoot: true,
        tagNameProcessors: [xml2js.processors.stripPrefix]
      }, (err, result) => {
        if (err) {
          reject(err);
          return;
        }

        try {
          const namedResource = result.namedResource;
          if (!namedResource) {
            resolve(null);
            return;
          }

          const config: HttpClientConfig = {
            name: namedResource.$.name || '',
            substitutionBindings: []
          };

          // 解析 TCP 详情和替换绑定
          const jndiConfig = namedResource.configuration;
          if (jndiConfig?.tcpDetails) {
            const tcpDetails = jndiConfig.tcpDetails;
            
            // 提取主机信息
            if (tcpDetails.$.host) {
              config.host = tcpDetails.$.host;
            }

            // 解析替换绑定
            if (tcpDetails.substitutionBindings) {
              const bindings = Array.isArray(tcpDetails.substitutionBindings) 
                ? tcpDetails.substitutionBindings 
                : [tcpDetails.substitutionBindings];

              for (const binding of bindings) {
                config.substitutionBindings.push({
                  template: binding.$.template,
                  propName: binding.$.propName
                });
              }
            }
          }

          resolve(config);
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  /**
   * 解析全局变量文件
   */
  private async parseGlobalVariables(projectPath: string, config: ParsedConfiguration): Promise<void> {
    // 查找 default.substvar 文件
    const substVarFiles = this.findFilesWithName(projectPath, 'default.substvar');
    
    for (const filePath of substVarFiles) {
      try {
        const variables = await this.parseSubstVarFile(filePath);
        for (const variable of variables) {
          config.globalVariables.set(variable.name, variable);
        }
      } catch (error) {
        console.warn(`Failed to parse substvar file: ${filePath}`, error);
      }
    }
  }

  /**
   * 解析 substvar 文件
   */
  private async parseSubstVarFile(filePath: string): Promise<GlobalVariable[]> {
    const content = readFileContent(filePath);
    
    return new Promise((resolve, reject) => {
      xml2js.parseString(content, {
        explicitArray: true,
        mergeAttrs: false,
        explicitRoot: true,
        tagNameProcessors: [xml2js.processors.stripPrefix]
      }, (err, result) => {
        if (err) {
          reject(err);
          return;
        }

        try {
          const variables: GlobalVariable[] = [];
          const repository = result.repository;
          
          if (repository?.globalVariables?.[0]?.globalVariable) {
            const globalVars = repository.globalVariables[0].globalVariable;

            for (const globalVar of globalVars) {
              const name = globalVar.name?.[0];
              const value = globalVar.value?.[0];
              const type = globalVar.type?.[0];

              if (name && value !== undefined) {
                variables.push({
                  name: this.cleanVariableName(name),
                  value,
                  type: type || 'String'
                });
              }
            }
          }

          resolve(variables);
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  /**
   * 构建服务 URL 映射
   */
  private buildServiceUrls(config: ParsedConfiguration): void {
    // 根据全局变量构建服务 URL
    const detailsHost = config.globalVariables.get('DetailsServiceHost')?.value;
    const detailsPort = config.globalVariables.get('DetailsServicePort')?.value;
    const searchHost = config.globalVariables.get('SearchServiceHost')?.value;
    const searchPort = config.globalVariables.get('SearchServicePort')?.value;
    const apiKey = config.globalVariables.get('apikey')?.value;

    if (detailsHost) {
      const port = detailsPort ? `:${detailsPort}` : '';
      const protocol = detailsPort === '443' ? 'https' : 'http';
      config.serviceUrls.set('details.service.url', `${protocol}://${detailsHost}${port}`);
    }

    if (searchHost) {
      const port = searchPort ? `:${searchPort}` : '';
      const protocol = searchPort === '443' ? 'https' : 'http';
      config.serviceUrls.set('search.service.url', `${protocol}://${searchHost}${port}`);
    }

    if (apiKey) {
      config.serviceUrls.set('omdb.api.key', apiKey);
    }

    // 设置 OMDB API URL（如果检测到是 OMDB 服务）
    if (detailsHost === 'www.omdbapi.com') {
      config.serviceUrls.set('omdb.api.url', 'http://www.omdbapi.com/');
    }
  }

  /**
   * 清理变量名（移除模块前缀）
   */
  private cleanVariableName(name: string): string {
    // 移除 //ModuleName// 前缀
    return name.replace(/^\/\/[^\/]+\/\//, '');
  }

  /**
   * 递归查找指定扩展名的文件
   */
  private findFilesWithExtension(dir: string, extension: string): string[] {
    const files: string[] = [];
    
    if (!fs.existsSync(dir)) {
      return files;
    }

    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files.push(...this.findFilesWithExtension(fullPath, extension));
      } else if (item.endsWith(extension)) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  /**
   * 递归查找指定名称的文件
   */
  private findFilesWithName(dir: string, fileName: string): string[] {
    const files: string[] = [];
    
    if (!fs.existsSync(dir)) {
      return files;
    }

    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files.push(...this.findFilesWithName(fullPath, fileName));
      } else if (item === fileName) {
        files.push(fullPath);
      }
    }
    
    return files;
  }
}
