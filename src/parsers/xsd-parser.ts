import * as xml2js from 'xml2js';
import {
  XSDSchema,
  XSDComplexType,
  XSDElement,
  ParsedClass,
  ParsedField
} from '../types/index';
import {
  xsdTypeToJavaType,
  generateSafeClassName,
  generateSafeFieldName,
  isOptionalField,
  isArrayField,
  extractNamespacePrefix
} from '../utils/type-utils';
import { readFileContent } from '../utils/file-utils';

/**
 * XSD Schema 解析器
 */
export class XSDParser {
  private parsedClasses: Map<string, ParsedClass> = new Map();
  private schema: XSDSchema | null = null;

  /**
   * 解析 XSD 文件
   */
  async parseXSD(filePath: string): Promise<ParsedClass[]> {
    const content = readFileContent(filePath);
    this.schema = await this.parseXMLToSchema(content);

    this.parsedClasses.clear();
    
    // 解析复杂类型
    if (this.schema.schema.complexType) {
      for (const complexType of this.schema.schema.complexType) {
        await this.parseComplexType(complexType, this.schema.schema.$.targetNamespace);
      }
    }

    // 解析根元素
    if (this.schema.schema.element) {
      for (const element of this.schema.schema.element) {
        await this.parseRootElement(element, this.schema.schema.$.targetNamespace);
      }
    }

    return Array.from(this.parsedClasses.values());
  }

  /**
   * 将 XML 字符串解析为 Schema 对象
   */
  private async parseXMLToSchema(content: string): Promise<XSDSchema> {
    return new Promise((resolve, reject) => {
      xml2js.parseString(content, {
        explicitArray: true,
        mergeAttrs: false,
        explicitRoot: true,
        tagNameProcessors: [xml2js.processors.stripPrefix]
      }, (err, result) => {
        if (err) {
          reject(new Error(`Failed to parse XML: ${err.message}`));
        } else {
          resolve(result as XSDSchema);
        }
      });
    });
  }

  /**
   * 解析复杂类型
   */
  private async parseComplexType(
    complexType: XSDComplexType,
    namespace?: string
  ): Promise<ParsedClass> {
    const className = generateSafeClassName(complexType.$.name);



    // 检查是否已经完全解析过（有字段的类）
    if (this.parsedClasses.has(className)) {
      const existingClass = this.parsedClasses.get(className)!;
      // 只有当类已经有字段时才返回，否则重新解析
      if (existingClass.fields.length > 0) {
        return existingClass;
      }
    }

    const parsedClass: ParsedClass = {
      name: className,
      namespace: namespace || '',
      fields: [],
      isRootElement: false
    };

    // 先不添加到缓存，等字段解析完成后再添加

    // 解析字段
    if (complexType.sequence) {
      for (const sequence of complexType.sequence) {
        if (sequence.element) {
          for (const element of sequence.element) {
            const field = await this.parseElement(element, namespace);
            parsedClass.fields.push(field);
          }
        }
      }
    }

    // 现在字段解析完成，添加到缓存
    this.parsedClasses.set(className, parsedClass);

    return parsedClass;
  }

  /**
   * 解析根元素
   */
  private async parseRootElement(
    element: XSDElement,
    namespace?: string
  ): Promise<ParsedClass> {
    const className = generateSafeClassName(element.$.name);

    // 不要立即检查缓存，因为我们需要确保字段被正确解析
    const parsedClass: ParsedClass = {
      name: className,
      namespace: namespace || '',
      fields: [],
      isRootElement: true
    };

    // 先不添加到缓存，等字段解析完成后再添加

    // 如果元素有类型引用
    if (element.$.type) {
      const referencedType = this.findComplexTypeByName(element.$.type);
      if (referencedType) {
        const referencedClass = await this.parseComplexType(referencedType, namespace);
        parsedClass.fields = referencedClass.fields;
      }
    }

    // 如果元素内嵌复杂类型
    if (element.complexType) {
      for (const complexType of element.complexType) {
        const complexTypeWithName = {
          $: { name: className },
          sequence: complexType.sequence
        };
        const inlineClass = await this.parseComplexType(
          complexTypeWithName,
          namespace
        );
        parsedClass.fields = inlineClass.fields;
      }
    }

    // 现在字段解析完成，添加到缓存
    this.parsedClasses.set(className, parsedClass);

    return parsedClass;
  }

  /**
   * 解析元素为字段
   */
  private async parseElement(
    element: XSDElement,
    namespace?: string
  ): Promise<ParsedField> {
    const originalName = element.$.name;  // 保存原始名称
    const fieldName = generateSafeFieldName(originalName);
    const isOptional = isOptionalField(element.$.minOccurs);
    const isArray = isArrayField(element.$.maxOccurs);

    let javaType: string;
    let isComplexType = false;
    let nestedClass: ParsedClass | undefined;

    if (element.$.type) {
      // 引用类型
      if (this.isBuiltInType(element.$.type)) {
        javaType = xsdTypeToJavaType(element.$.type);
      } else {
        // 自定义复杂类型
        javaType = generateSafeClassName(this.extractTypeName(element.$.type));
        isComplexType = true;
        
        const referencedType = this.findComplexTypeByName(element.$.type);
        if (referencedType) {
          nestedClass = await this.parseComplexType(referencedType, namespace);
        }
      }
    } else if (element.complexType) {
      // 内联复杂类型
      const inlineTypeName = `${fieldName}Type`;
      javaType = generateSafeClassName(inlineTypeName);
      isComplexType = true;
      
      nestedClass = await this.parseComplexType(
        {
          $: { name: inlineTypeName },
          sequence: element.complexType[0].sequence
        },
        namespace
      );
    } else {
      // 默认为字符串类型
      javaType = 'String';
    }

    // 处理数组类型
    if (isArray) {
      javaType = `List<${javaType}>`;
    }

    return {
      name: fieldName,
      originalName: originalName,
      type: element.$.type || 'string',
      javaType,
      isOptional,
      isArray,
      isComplexType,
      nestedClass
    };
  }

  /**
   * 查找复杂类型定义
   */
  private findComplexTypeByName(typeName: string): XSDComplexType | null {
    if (!this.schema || !this.schema.schema.complexType) {
      return null;
    }

    const cleanTypeName = this.extractTypeName(typeName);

    for (const complexType of this.schema.schema.complexType) {
      if (complexType.$.name === cleanTypeName) {
        return complexType;
      }
    }

    return null;
  }

  /**
   * 判断是否为内置类型
   */
  private isBuiltInType(typeName: string): boolean {
    const builtInTypes = [
      'string', 'xs:string',
      'int', 'xs:int',
      'integer', 'xs:integer',
      'long', 'xs:long',
      'double', 'xs:double',
      'float', 'xs:float',
      'boolean', 'xs:boolean',
      'date', 'xs:date',
      'dateTime', 'xs:dateTime',
      'time', 'xs:time',
      'decimal', 'xs:decimal'
    ];
    
    return builtInTypes.includes(typeName);
  }

  /**
   * 提取类型名称（去除命名空间前缀）
   */
  private extractTypeName(typeName: string): string {
    if (typeName.includes(':')) {
      const parts = typeName.split(':');
      return parts[parts.length - 1];
    }
    return typeName;
  }

  /**
   * 获取所有解析的类
   */
  getAllParsedClasses(): ParsedClass[] {
    return Array.from(this.parsedClasses.values());
  }

  /**
   * 根据名称获取解析的类
   */
  getParsedClassByName(name: string): ParsedClass | undefined {
    return this.parsedClasses.get(name);
  }
}
