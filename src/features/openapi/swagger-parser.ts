import * as fs from 'fs';
import * as path from 'path';
import { SwaggerSpec, PathItem, Operation, Parameter, SchemaObject, ApiTestCase, TestParameter, ExpectedResponse } from '../../types';

/**
 * Swagger/OpenAPI 解析器
 * 负责解析 swagger.json 文件并提取 API 信息
 */
export class SwaggerParser {
  private swaggerSpec: SwaggerSpec | null = null;

  /**
   * 解析 Swagger JSON 文件
   */
  async parseSwaggerFile(filePath: string): Promise<SwaggerSpec> {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error(`Swagger file not found: ${filePath}`);
      }

      const content = fs.readFileSync(filePath, 'utf-8');
      this.swaggerSpec = JSON.parse(content);

      if (!this.swaggerSpec) {
        throw new Error('Failed to parse swagger.json');
      }

      // 验证基本结构
      this.validateSwaggerSpec(this.swaggerSpec);

      console.log(`✅ Successfully parsed Swagger spec: ${this.swaggerSpec.info.title} v${this.swaggerSpec.info.version}`);
      return this.swaggerSpec;
    } catch (error) {
      throw new Error(`Failed to parse Swagger file: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 验证 Swagger 规范的基本结构
   */
  private validateSwaggerSpec(spec: SwaggerSpec): void {
    if (!spec.info || !spec.info.title || !spec.info.version) {
      throw new Error('Invalid Swagger spec: missing required info fields');
    }

    if (!spec.paths || Object.keys(spec.paths).length === 0) {
      throw new Error('Invalid Swagger spec: no paths defined');
    }
  }

  /**
   * 获取所有 API 端点信息
   */
  getApiEndpoints(): Array<{
    path: string;
    method: string;
    operation: Operation;
    parameters: Parameter[];
  }> {
    if (!this.swaggerSpec) {
      throw new Error('No swagger spec loaded. Call parseSwaggerFile first.');
    }

    const endpoints: Array<{
      path: string;
      method: string;
      operation: Operation;
      parameters: Parameter[];
    }> = [];

    for (const [pathPattern, pathItem] of Object.entries(this.swaggerSpec.paths)) {
      const methods = ['get', 'post', 'put', 'delete', 'patch', 'options', 'head'] as const;
      
      for (const method of methods) {
        const operation = pathItem[method];
        if (operation) {
          endpoints.push({
            path: pathPattern,
            method: method.toUpperCase(),
            operation,
            parameters: operation.parameters || []
          });
        }
      }
    }

    return endpoints;
  }

  /**
   * 生成 API 测试用例
   */
  generateTestCases(): ApiTestCase[] {
    const endpoints = this.getApiEndpoints();
    const testCases: ApiTestCase[] = [];

    for (const endpoint of endpoints) {
      const testCase = this.createTestCaseFromEndpoint(endpoint);
      testCases.push(testCase);
    }

    return testCases;
  }

  /**
   * 从端点信息创建测试用例
   */
  private createTestCaseFromEndpoint(endpoint: {
    path: string;
    method: string;
    operation: Operation;
    parameters: Parameter[];
  }): ApiTestCase {
    const testParameters: TestParameter[] = [];

    // 处理参数
    for (const param of endpoint.parameters) {
      const testParam: TestParameter = {
        name: param.name,
        type: param.type || 'string',
        value: this.generateTestValue(param),
        required: param.required || false,
        location: param.in
      };
      testParameters.push(testParam);
    }

    // 获取预期响应
    const expectedResponse = this.getExpectedResponse(endpoint.operation);

    return {
      name: `test${this.toPascalCase(endpoint.operation.operationId || endpoint.method + endpoint.path.replace(/[^a-zA-Z0-9]/g, ''))}`,
      method: endpoint.method,
      path: endpoint.path,
      parameters: testParameters,
      expectedResponse,
      description: endpoint.operation.summary || endpoint.operation.description
    };
  }

  /**
   * 生成测试参数值
   */
  private generateTestValue(param: Parameter): any {
    switch (param.type) {
      case 'string':
        if (param.name.toLowerCase().includes('search')) {
          return 'test';
        }
        return 'testValue';
      case 'integer':
      case 'number':
        return 123;
      case 'boolean':
        return true;
      case 'array':
        return ['testItem'];
      default:
        return 'testValue';
    }
  }

  /**
   * 获取预期响应信息
   */
  private getExpectedResponse(operation: Operation): ExpectedResponse {
    // 查找成功响应（通常是 200）
    const successResponse = operation.responses['200'] || operation.responses['201'] || operation.responses['default'];
    
    if (!successResponse) {
      return {
        statusCode: 200,
        contentType: 'application/json'
      };
    }

    const expectedResponse: ExpectedResponse = {
      statusCode: 200,
      contentType: 'application/json',
      schema: successResponse.schema
    };

    // 如果有 schema，提取属性名
    if (successResponse.schema) {
      expectedResponse.properties = this.extractSchemaProperties(successResponse.schema);
    }

    return expectedResponse;
  }

  /**
   * 提取 Schema 属性
   */
  private extractSchemaProperties(schema: SchemaObject): string[] {
    const properties: string[] = [];

    if (schema.$ref) {
      // 处理引用类型
      const refName = schema.$ref.split('/').pop();
      if (refName && this.swaggerSpec?.definitions?.[refName]) {
        const refSchema = this.swaggerSpec.definitions[refName];
        return this.extractSchemaProperties(refSchema);
      }
    }

    if (schema.properties) {
      properties.push(...Object.keys(schema.properties));
    }

    if (schema.type === 'array' && schema.items) {
      return this.extractSchemaProperties(schema.items);
    }

    return properties;
  }

  /**
   * 转换为 PascalCase
   */
  private toPascalCase(str: string): string {
    return str
      .replace(/[^a-zA-Z0-9]/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');
  }

  /**
   * 获取基础 URL
   */
  getBaseUrl(): string {
    if (!this.swaggerSpec) {
      return 'http://localhost:8080';
    }

    const scheme = this.swaggerSpec.schemes?.[0] || 'http';
    const host = this.swaggerSpec.host || 'localhost:8080';
    const basePath = this.swaggerSpec.basePath || '';

    return `${scheme}://${host}${basePath}`;
  }

  /**
   * 获取 API 信息
   */
  getApiInfo(): { title: string; version: string; description?: string } {
    if (!this.swaggerSpec) {
      throw new Error('No swagger spec loaded');
    }

    return {
      title: this.swaggerSpec.info.title,
      version: this.swaggerSpec.info.version,
      description: this.swaggerSpec.info.description
    };
  }

  /**
   * 获取所有定义的模型
   */
  getModels(): Record<string, SchemaObject> {
    if (!this.swaggerSpec) {
      return {};
    }

    return this.swaggerSpec.definitions || this.swaggerSpec.components?.schemas || {};
  }
}
