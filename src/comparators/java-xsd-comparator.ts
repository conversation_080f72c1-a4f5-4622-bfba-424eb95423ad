import { ParsedClass, ParsedField, ComparisonResult, Difference } from '../types/index';
import { readFileContent, fileExists } from '../utils/file-utils';
import * as path from 'path';

/**
 * Java 类与 XSD 差异对比器
 */
export class JavaXSDComparator {
  
  /**
   * 对比生成的 Java 类与现有的 Java 实现
   */
  compareWithExistingJava(
    parsedClasses: ParsedClass[],
    existingJavaDir: string,
    packageName: string
  ): ComparisonResult[] {
    const results: ComparisonResult[] = [];

    for (const parsedClass of parsedClasses) {
      const existingJavaPath = this.getJavaFilePath(
        existingJavaDir,
        packageName,
        parsedClass.name
      );

      if (fileExists(existingJavaPath)) {
        const existingJavaContent = readFileContent(existingJavaPath);
        const differences = this.compareClass(parsedClass, existingJavaContent);
        
        results.push({
          className: parsedClass.name,
          differences
        });
      } else {
        results.push({
          className: parsedClass.name,
          differences: [{
            type: 'missing_field',
            field: '',
            description: `Java class ${parsedClass.name} does not exist`
          }]
        });
      }
    }

    return results;
  }

  /**
   * 对比单个类
   */
  private compareClass(parsedClass: ParsedClass, javaContent: string): Difference[] {
    const differences: Difference[] = [];
    const javaFields = this.extractFieldsFromJavaContent(javaContent);
    
    // 检查 XSD 中定义的字段是否在 Java 中存在
    for (const xsdField of parsedClass.fields) {
      const javaField = javaFields.find(f => f.name === xsdField.name);
      
      if (!javaField) {
        differences.push({
          type: 'missing_field',
          field: xsdField.name,
          expected: xsdField.javaType,
          description: `Field '${xsdField.name}' of type '${xsdField.javaType}' is missing in Java class`
        });
      } else {
        // 检查类型是否匹配
        const expectedType = this.normalizeJavaType(xsdField.javaType);
        const actualType = this.normalizeJavaType(javaField.type);
        
        if (expectedType !== actualType) {
          differences.push({
            type: 'type_mismatch',
            field: xsdField.name,
            expected: expectedType,
            actual: actualType,
            description: `Field '${xsdField.name}' has type mismatch. Expected: ${expectedType}, Actual: ${actualType}`
          });
        }

        // 检查注解
        this.compareAnnotations(xsdField, javaField, differences);
      }
    }

    // 检查 Java 中额外的字段
    for (const javaField of javaFields) {
      const xsdField = parsedClass.fields.find(f => f.name === javaField.name);
      
      if (!xsdField) {
        differences.push({
          type: 'extra_field',
          field: javaField.name,
          actual: javaField.type,
          description: `Field '${javaField.name}' exists in Java class but not defined in XSD`
        });
      }
    }

    return differences;
  }

  /**
   * 从 Java 代码中提取字段信息
   */
  private extractFieldsFromJavaContent(javaContent: string): JavaField[] {
    const fields: JavaField[] = [];
    
    // 简单的正则表达式匹配字段声明
    // 这是一个简化的实现，实际中可能需要更复杂的 Java 解析器
    const fieldRegex = /private\s+([^;]+?)\s+(\w+)\s*(?:=\s*[^;]+)?\s*;/g;
    let match;

    while ((match = fieldRegex.exec(javaContent)) !== null) {
      const type = match[1].trim();
      const name = match[2].trim();
      
      // 提取该字段的注解
      const annotations = this.extractFieldAnnotations(javaContent, name);
      
      fields.push({
        name,
        type,
        annotations
      });
    }

    return fields;
  }

  /**
   * 提取字段的注解
   */
  private extractFieldAnnotations(javaContent: string, fieldName: string): string[] {
    const annotations: string[] = [];
    
    // 查找字段声明前的注解
    const fieldPattern = new RegExp(`((?:@\\w+[^\\r\\n]*\\r?\\n\\s*)*?)private\\s+[^;]+?\\s+${fieldName}\\s*;`, 'g');
    const match = fieldPattern.exec(javaContent);
    
    if (match && match[1]) {
      const annotationPattern = /@(\w+)(?:\([^)]*\))?/g;
      let annotationMatch;
      
      while ((annotationMatch = annotationPattern.exec(match[1])) !== null) {
        annotations.push(annotationMatch[1]);
      }
    }

    return annotations;
  }

  /**
   * 对比注解
   */
  private compareAnnotations(
    xsdField: ParsedField,
    javaField: JavaField,
    differences: Difference[]
  ): void {
    const expectedAnnotations = this.getExpectedAnnotations(xsdField);
    
    for (const expectedAnnotation of expectedAnnotations) {
      if (!javaField.annotations.includes(expectedAnnotation)) {
        differences.push({
          type: 'annotation_mismatch',
          field: xsdField.name,
          expected: expectedAnnotation,
          description: `Field '${xsdField.name}' is missing annotation @${expectedAnnotation}`
        });
      }
    }
  }

  /**
   * 获取字段期望的注解
   */
  private getExpectedAnnotations(field: ParsedField): string[] {
    const annotations: string[] = [];

    if (!field.isOptional) {
      annotations.push('NotNull');
    }

    if (field.javaType === 'String' && !field.isOptional) {
      annotations.push('NotBlank');
    }

    if (field.isComplexType) {
      annotations.push('Valid');
    }

    return annotations;
  }

  /**
   * 标准化 Java 类型名称
   */
  private normalizeJavaType(type: string): string {
    // 移除泛型参数中的空格
    return type.replace(/\s+/g, '');
  }

  /**
   * 获取 Java 文件路径
   */
  private getJavaFilePath(baseDir: string, packageName: string, className: string): string {
    const packagePath = packageName.replace(/\./g, path.sep);
    return path.join(baseDir, packagePath, `${className}.java`);
  }

  /**
   * 生成差异报告
   */
  generateReport(results: ComparisonResult[]): string {
    let report = '# XSD to Java Comparison Report\n\n';
    
    let totalDifferences = 0;
    
    for (const result of results) {
      report += `## Class: ${result.className}\n\n`;
      
      if (result.differences.length === 0) {
        report += '✅ No differences found\n\n';
      } else {
        report += `❌ Found ${result.differences.length} difference(s):\n\n`;
        
        for (const diff of result.differences) {
          report += `- **${diff.type.replace('_', ' ').toUpperCase()}**: ${diff.description}\n`;
          if (diff.expected) {
            report += `  - Expected: \`${diff.expected}\`\n`;
          }
          if (diff.actual) {
            report += `  - Actual: \`${diff.actual}\`\n`;
          }
          report += '\n';
        }
        
        totalDifferences += result.differences.length;
      }
    }

    report += `## Summary\n\n`;
    report += `Total classes analyzed: ${results.length}\n`;
    report += `Total differences found: ${totalDifferences}\n`;

    if (totalDifferences === 0) {
      report += '\n🎉 All Java classes are in sync with XSD definitions!';
    } else {
      report += '\n⚠️ Some differences were found. Please review and update accordingly.';
    }

    return report;
  }
}

/**
 * Java 字段信息
 */
interface JavaField {
  name: string;
  type: string;
  annotations: string[];
}
