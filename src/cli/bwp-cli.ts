#!/usr/bin/env node

import { Command } from 'commander';
import { BWPParser } from '../parsers/bwp-parser';
import { BWPJavaGenerator } from '../generators/bwp-java-generator';
import { JavaGenerationOptions } from '../types';
import { writeFileSync, mkdirSync } from 'fs';
import { dirname, join } from 'path';
import chalk from 'chalk';
import ora from 'ora';

const program = new Command();

program
  .name('bwp-converter')
  .description('Convert TIBCO BW .bwp files to Java Spring Boot code')
  .version('1.0.0');

program
  .command('convert')
  .description('Convert a .bwp file to Java Spring Boot code')
  .requiredOption('-i, --input <file>', 'Input .bwp file path')
  .requiredOption('-o, --output <dir>', 'Output directory for generated Java files')
  .option('-p, --package <name>', 'Java package name', 'com.example.generated')
  .option('--lombok', 'Use Lombok annotations', false)
  .option('--validation', 'Use JSR-303 validation annotations', true)
  .option('--jackson', 'Use Jackson annotations', true)
  .action(async (options) => {
    const spinner = ora('Processing BWP file...').start();
    
    try {
      // Parse BWP file
      spinner.text = 'Parsing BWP file...';
      const parser = new BWPParser();
      const parsedProcess = await parser.parseBWP(options.input);
      
      spinner.text = 'Generating Java code...';
      
      // Setup generation options
      const generationOptions: JavaGenerationOptions = {
        packageName: options.package,
        outputDir: options.output,
        useLombok: options.lombok,
        useJSR303Validation: options.validation,
        useJacksonAnnotations: options.jackson,
        includeConstructors: true,
        includeToString: true
      };
      
      // Generate Java code
      const generator = new BWPJavaGenerator(generationOptions);
      const controllerCode = generator.generateController(parsedProcess);
      const serviceCode = generator.generateService(parsedProcess);
      
      // Create output directories
      const packagePath = options.package.replace(/\./g, '/');
      const controllerDir = join(options.output, 'src/main/java', packagePath, 'controller');
      const serviceDir = join(options.output, 'src/main/java', packagePath, 'service');
      
      mkdirSync(controllerDir, { recursive: true });
      mkdirSync(serviceDir, { recursive: true });
      
      // Write files
      const controllerFile = join(controllerDir, `${parsedProcess.name}Controller.java`);
      const serviceFile = join(serviceDir, `${parsedProcess.name}Service.java`);
      
      writeFileSync(controllerFile, controllerCode);
      writeFileSync(serviceFile, serviceCode);
      
      spinner.succeed('Java code generated successfully!');
      
      console.log(chalk.green('\n✅ Generated files:'));
      console.log(chalk.blue(`   Controller: ${controllerFile}`));
      console.log(chalk.blue(`   Service: ${serviceFile}`));
      
      console.log(chalk.yellow('\n📋 Process Summary:'));
      console.log(`   Process Name: ${parsedProcess.name}`);
      console.log(`   Input Type: ${parsedProcess.interface.inputType}`);
      console.log(`   Output Type: ${parsedProcess.interface.outputType}`);
      console.log(`   REST Endpoints: ${parsedProcess.restEndpoints.length}`);
      console.log(`   Variables: ${parsedProcess.variables.length}`);
      console.log(`   Partner Links: ${parsedProcess.partnerLinks.length}`);
      
      if (parsedProcess.restEndpoints.length > 0) {
        console.log(chalk.cyan('\n🌐 REST Endpoints:'));
        parsedProcess.restEndpoints.forEach(endpoint => {
          console.log(`   ${endpoint.method} ${endpoint.path} -> ${endpoint.operationName}`);
        });
      }
      
    } catch (error) {
      spinner.fail('Failed to process BWP file');
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : error);
      process.exit(1);
    }
  });

program
  .command('analyze')
  .description('Analyze a .bwp file and show its structure')
  .requiredOption('-i, --input <file>', 'Input .bwp file path')
  .action(async (options) => {
    const spinner = ora('Analyzing BWP file...').start();
    
    try {
      const parser = new BWPParser();
      const parsedProcess = await parser.parseBWP(options.input);
      
      spinner.succeed('Analysis complete!');
      
      console.log(chalk.green('\n📊 BWP File Analysis'));
      console.log(chalk.blue('='.repeat(50)));
      
      console.log(chalk.yellow('\n🏗️  Process Information:'));
      console.log(`   Name: ${parsedProcess.name}`);
      console.log(`   Namespace: ${parsedProcess.namespace}`);
      console.log(`   Callable: ${parsedProcess.processInfo.callable}`);
      console.log(`   Stateless: ${parsedProcess.processInfo.stateless}`);
      console.log(`   Type: ${parsedProcess.processInfo.type}`);
      
      console.log(chalk.yellow('\n🔌 Interface:'));
      console.log(`   Input: ${parsedProcess.interface.inputType}`);
      console.log(`   Output: ${parsedProcess.interface.outputType}`);
      
      if (parsedProcess.variables.length > 0) {
        console.log(chalk.yellow('\n📝 Variables:'));
        parsedProcess.variables.forEach(variable => {
          console.log(`   ${variable.name} (${variable.type}): ${variable.dataType}${variable.parameterType ? ` [${variable.parameterType}]` : ''}`);
        });
      }
      
      if (parsedProcess.partnerLinks.length > 0) {
        console.log(chalk.yellow('\n🔗 Partner Links:'));
        parsedProcess.partnerLinks.forEach(link => {
          console.log(`   ${link.name}: ${link.partnerLinkType} (${link.role})`);
          if (link.restBinding) {
            console.log(`     REST: ${link.restBinding.path} (${link.restBinding.operations.length} operations)`);
          }
        });
      }
      
      if (parsedProcess.restEndpoints.length > 0) {
        console.log(chalk.yellow('\n🌐 REST Endpoints:'));
        parsedProcess.restEndpoints.forEach(endpoint => {
          console.log(`   ${endpoint.method} ${endpoint.path}`);
          console.log(`     Operation: ${endpoint.operationName}`);
          console.log(`     Input: ${endpoint.inputType}`);
          console.log(`     Output: ${endpoint.outputType}`);
          if (endpoint.parameters.length > 0) {
            console.log(`     Parameters:`);
            endpoint.parameters.forEach(param => {
              console.log(`       - ${param.name} (${param.dataType}, ${param.parameterType})${param.required ? ' *required' : ''}`);
            });
          }
        });
      }
      
      if (parsedProcess.activities.length > 0) {
        console.log(chalk.yellow('\n⚡ Activities:'));
        parsedProcess.activities.forEach(activity => {
          console.log(`   ${activity.name} (${activity.type})`);
          if (activity.partnerLink) {
            console.log(`     Partner: ${activity.partnerLink}`);
          }
          if (activity.operation) {
            console.log(`     Operation: ${activity.operation}`);
          }
        });
      }
      
    } catch (error) {
      spinner.fail('Failed to analyze BWP file');
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : error);
      process.exit(1);
    }
  });

if (process.argv.length < 3) {
  program.help();
}

program.parse();
