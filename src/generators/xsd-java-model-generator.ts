import { XSDParser } from '../parsers/xsd-parser';
import { JavaCodeGenerator } from './java-generator';
import { ParsedClass, JavaGenerationOptions } from '../types/index';
import { findXSDFiles } from '../utils/file-utils';
import * as path from 'path';

/**
 * XSD 到 Java 模型类生成器
 * 专门用于从 BWP 流程相关的 XSD 文件生成 Java POJO 类
 */
export class XSDJavaModelGenerator {
  private xsdParser: XSDParser;
  private javaGenerator: JavaCodeGenerator;
  private options: JavaGenerationOptions;

  constructor(options: JavaGenerationOptions) {
    this.options = options;
    this.xsdParser = new XSDParser();
    this.javaGenerator = new JavaCodeGenerator(options);
  }

  /**
   * 从指定目录生成所有 XSD 对应的 Java 模型类
   */
  async generateModelsFromDirectory(xsdDirectory: string): Promise<ParsedClass[]> {
    const xsdFiles = findXSDFiles(xsdDirectory);
    const allClasses: ParsedClass[] = [];

    console.log(`Found ${xsdFiles.length} XSD files in ${xsdDirectory}`);

    for (const xsdFile of xsdFiles) {
      try {
        console.log(`Processing XSD file: ${xsdFile}`);
        const classes = await this.xsdParser.parseXSD(xsdFile);
        
        // 生成 Java 代码
        for (const parsedClass of classes) {
          this.javaGenerator.generateClass(parsedClass);
          allClasses.push(parsedClass);
        }
        
        console.log(`Generated ${classes.length} Java classes from ${path.basename(xsdFile)}`);
      } catch (error) {
        console.error(`Error processing XSD file ${xsdFile}:`, error);
      }
    }

    return allClasses;
  }

  /**
   * 从单个 XSD 文件生成 Java 模型类
   */
  async generateModelsFromFile(xsdFilePath: string): Promise<ParsedClass[]> {
    try {
      console.log(`Processing XSD file: ${xsdFilePath}`);
      const classes = await this.xsdParser.parseXSD(xsdFilePath);
      
      // 生成 Java 代码
      for (const parsedClass of classes) {
        this.javaGenerator.generateClass(parsedClass);
      }
      
      console.log(`Generated ${classes.length} Java classes from ${path.basename(xsdFilePath)}`);
      return classes;
    } catch (error) {
      console.error(`Error processing XSD file ${xsdFilePath}:`, error);
      throw error;
    }
  }

  /**
   * 为 BWP 流程生成特定的模型类
   * 基于 BWP 流程的输入输出类型生成对应的 Java 类
   */
  async generateModelsForBWPProcess(
    inputType: string,
    outputType: string,
    inputNamespace?: string,
    outputNamespace?: string,
    xsdDirectory?: string
  ): Promise<{ inputClass?: ParsedClass; outputClass?: ParsedClass }> {
    const result: { inputClass?: ParsedClass; outputClass?: ParsedClass } = {};

    if (xsdDirectory) {
      // 从 XSD 目录中查找相关的类型定义
      const allClasses = await this.generateModelsFromDirectory(xsdDirectory);
      
      // 查找输入类型
      if (inputType && inputType !== 'void') {
        result.inputClass = allClasses.find(cls => 
          cls.name === inputType || cls.name === this.capitalizeTypeName(inputType)
        );
      }
      
      // 查找输出类型
      if (outputType && outputType !== 'void') {
        result.outputClass = allClasses.find(cls => 
          cls.name === outputType || cls.name === this.capitalizeTypeName(outputType)
        );
      }
    }

    return result;
  }

  /**
   * 生成 BWP 流程所需的所有模型类
   * 这是一个便捷方法，用于生成完整的模型类集合
   */
  async generateAllModelsForBWP(xsdDirectory: string): Promise<{
    allClasses: ParsedClass[];
    inputOutputMapping: Map<string, ParsedClass>;
  }> {
    const allClasses = await this.generateModelsFromDirectory(xsdDirectory);
    const inputOutputMapping = new Map<string, ParsedClass>();

    // 创建类型名称到类的映射，便于 BWP 生成器查找
    for (const parsedClass of allClasses) {
      inputOutputMapping.set(parsedClass.name, parsedClass);
      inputOutputMapping.set(parsedClass.name.toLowerCase(), parsedClass);
      
      // 如果是根元素，也添加到映射中
      if (parsedClass.isRootElement) {
        inputOutputMapping.set(`root_${parsedClass.name}`, parsedClass);
      }
    }

    return {
      allClasses,
      inputOutputMapping
    };
  }

  /**
   * 获取生成的 Java 代码字符串（用于测试）
   */
  async generateJavaCodeString(xsdFilePath: string): Promise<string[]> {
    const classes = await this.xsdParser.parseXSD(xsdFilePath);
    const javaCodeStrings: string[] = [];

    for (const parsedClass of classes) {
      // 使用私有方法生成 Java 代码字符串
      const javaCode = (this.javaGenerator as any).generateJavaCode(parsedClass);
      javaCodeStrings.push(javaCode);
    }

    return javaCodeStrings;
  }

  /**
   * 工具方法：将类型名称首字母大写
   */
  private capitalizeTypeName(typeName: string): string {
    return typeName.charAt(0).toUpperCase() + typeName.slice(1);
  }

  /**
   * 获取解析器实例（用于测试）
   */
  getParser(): XSDParser {
    return this.xsdParser;
  }

  /**
   * 获取 Java 生成器实例（用于测试）
   */
  getJavaGenerator(): JavaCodeGenerator {
    return this.javaGenerator;
  }
}
