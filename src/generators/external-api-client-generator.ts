import { ParsedRestBinding, ParsedRestOperation } from '../types/index';
import { ParsedConfiguration } from '../parsers/config-parser';

/**
 * 外部 API 客户端生成器
 * 类似 FeignClient 的通用外部 API 调用生成
 */
export class ExternalApiClientGenerator {
  private config: ParsedConfiguration;

  constructor(config: ParsedConfiguration) {
    this.config = config;
  }

  /**
   * 生成外部 API 客户端接口
   */
  generateApiClient(binding: ParsedRestBinding, serviceName: string): string {
    const className = this.generateClientClassName(serviceName);
    const baseUrl = this.resolveBaseUrl(binding);
    
    const methods = binding.operations.map(op => 
      this.generateClientMethod(op, binding)
    ).join('\n\n');

    return `
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;

/**
 * External API Client for ${serviceName}
 * Generated from Tibco BW REST binding
 */
@FeignClient(
    name = "${serviceName.toLowerCase()}",
    url = "\${${this.getConfigKey(serviceName)}.url}"
)
public interface ${className} {

${methods}

}`;
  }

  /**
   * 生成客户端方法
   */
  private generateClientMethod(operation: ParsedRestOperation, binding: ParsedRestBinding): string {
    const methodName = this.toCamelCase(operation.name);
    const httpMethod = operation.httpMethod.toUpperCase();
    const path = this.buildPath(binding.path, operation);
    
    // 生成参数
    const parameters = operation.parameters.map(param => {
      const javaType = this.mapDataTypeToJava(param.dataType);
      const paramName = this.toCamelCase(param.name);
      
      if (param.parameterType === 'Query') {
        return `@RequestParam("${param.name}") ${javaType} ${paramName}`;
      } else if (param.parameterType === 'Path') {
        return `@PathVariable("${param.name}") ${javaType} ${paramName}`;
      } else {
        return `@RequestParam("${param.name}") ${javaType} ${paramName}`;
      }
    }).join(', ');

    // 生成方法注解
    const mappingAnnotation = this.generateMappingAnnotation(httpMethod, path);
    
    return `    ${mappingAnnotation}
    ResponseEntity<String> ${methodName}(${parameters});`;
  }

  /**
   * 生成映射注解
   */
  private generateMappingAnnotation(httpMethod: string, path: string): string {
    switch (httpMethod) {
      case 'GET':
        return `@GetMapping("${path}")`;
      case 'POST':
        return `@PostMapping("${path}")`;
      case 'PUT':
        return `@PutMapping("${path}")`;
      case 'DELETE':
        return `@DeleteMapping("${path}")`;
      default:
        return `@RequestMapping(method = RequestMethod.${httpMethod}, path = "${path}")`;
    }
  }

  /**
   * 构建路径
   */
  private buildPath(basePath: string, operation: ParsedRestOperation): string {
    // 如果 basePath 是根路径，直接返回操作路径
    if (basePath === '/' || !basePath) {
      return '/';
    }
    return basePath;
  }

  /**
   * 解析基础 URL
   */
  private resolveBaseUrl(binding: ParsedRestBinding): string {
    if (binding.docBasePath) {
      return binding.docBasePath;
    }
    
    // 从连接器配置中获取 URL
    if (binding.connector && this.config.httpClients.has(binding.connector)) {
      const httpClient = this.config.httpClients.get(binding.connector)!;
      if (httpClient.host) {
        const port = httpClient.substitutionBindings.find((b: any) => b.template === 'port')?.propName;
        const portValue = port ? this.config.globalVariables.get(port)?.value || '80' : '80';
        const protocol = portValue === '443' ? 'https' : 'http';
        return `${protocol}://${httpClient.host}${portValue && portValue !== '80' && portValue !== '443' ? ':' + portValue : ''}`;
      }
    }
    
    return 'http://localhost:8080';
  }

  /**
   * 生成客户端类名
   */
  private generateClientClassName(serviceName: string): string {
    return this.toPascalCase(serviceName) + 'Client';
  }

  /**
   * 获取配置键
   */
  private getConfigKey(serviceName: string): string {
    return serviceName.toLowerCase().replace(/[^a-z0-9]/g, '.');
  }

  /**
   * 数据类型映射
   */
  private mapDataTypeToJava(dataType: string): string {
    switch (dataType?.toLowerCase()) {
      case 'string':
        return 'String';
      case 'int':
      case 'integer':
        return 'Integer';
      case 'long':
        return 'Long';
      case 'double':
        return 'Double';
      case 'float':
        return 'Float';
      case 'boolean':
        return 'Boolean';
      case 'date':
        return 'LocalDate';
      case 'datetime':
        return 'LocalDateTime';
      default:
        return 'String';
    }
  }

  /**
   * 转换为驼峰命名
   */
  private toCamelCase(str: string): string {
    return str.replace(/[-_](.)/g, (_, char) => char.toUpperCase())
              .replace(/^./, char => char.toLowerCase());
  }

  /**
   * 转换为帕斯卡命名
   */
  private toPascalCase(str: string): string {
    return str.replace(/[-_](.)/g, (_, char) => char.toUpperCase())
              .replace(/^./, char => char.toUpperCase());
  }

  /**
   * 生成服务实现类
   */
  generateServiceImplementation(binding: ParsedRestBinding, serviceName: string): string {
    const className = this.toPascalCase(serviceName) + 'Service';
    const clientName = this.generateClientClassName(serviceName);
    const clientFieldName = this.toCamelCase(clientName);
    
    const methods = binding.operations.map(op => 
      this.generateServiceMethod(op, clientFieldName)
    ).join('\n\n');

    return `
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.http.ResponseEntity;

/**
 * Service implementation for ${serviceName}
 */
@Service
public class ${className} {

    @Autowired
    private ${clientName} ${clientFieldName};

${methods}

}`;
  }

  /**
   * 生成服务方法
   */
  private generateServiceMethod(operation: ParsedRestOperation, clientFieldName: string): string {
    const methodName = this.toCamelCase(operation.name);
    const parameters = operation.parameters.map(param => {
      const javaType = this.mapDataTypeToJava(param.dataType);
      const paramName = this.toCamelCase(param.name);
      return `${javaType} ${paramName}`;
    }).join(', ');

    const paramNames = operation.parameters.map(param => 
      this.toCamelCase(param.name)
    ).join(', ');

    return `    public ResponseEntity<String> ${methodName}(${parameters}) {
        return ${clientFieldName}.${methodName}(${paramNames});
    }`;
  }
}
