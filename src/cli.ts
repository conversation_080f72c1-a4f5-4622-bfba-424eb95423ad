#!/usr/bin/env node

import { Command } from 'commander';
import * as path from 'path';
import * as fs from 'fs';
import { BWPParser } from './parsers/bwp-parser';
import { ConfigParser } from './parsers/config-parser';
import { BWPJavaGenerator } from './generators/bwp-java-generator';
import { XSDJavaModelGenerator } from './generators/xsd-java-model-generator';
import { PropertiesGenerator } from './generators/properties-generator';
import { SpringBootDeployer } from './utils/spring-boot-deployer';
import { SwaggerParser } from './features/openapi/swagger-parser';
import { ApiTestGenerator } from './features/openapi/api-test-generator';
import { JavaGenerationOptions, ApiTestGenerationOptions } from './types';

const program = new Command();

program
  .name('tibco-bw-to-springboot')
  .description('Convert Tibco BusinessWorks (.bwp) files to Spring Boot Java applications')
  .version('1.0.0')
  .addHelpText('after', `
Examples:
  # Complete auto conversion with API testing (RECOMMENDED)
  $ tibco-bw-to-springboot auto test/_fixtures/

  # Auto conversion without app startup
  $ tibco-bw-to-springboot auto test/_fixtures/ --no-app-start

  # Generate API tests only
  $ tibco-bw-to-springboot test-api test/_fixtures/ --no-start-app

  # Quick convert specific BWP file
  $ tibco-bw-to-springboot quick path/to/file.bwp

  # Convert with custom package and port
  $ tibco-bw-to-springboot auto test/_fixtures/ -p com.mycompany.api --port 9090

  # Skip test generation
  $ tibco-bw-to-springboot auto test/_fixtures/ --no-test-generation

Features:
  ✅ BWP file parsing and Java code generation
  ✅ XSD schema to Java model conversion
  ✅ Swagger/OpenAPI parsing and API test generation
  ✅ Spring Boot application startup and validation
  ✅ End-to-end automated workflow

For more information, visit: https://github.com/aise-workshop/tibco-movie-example
`);

// 最简化的自动转换命令 - 只需要指定 Tibco BW 目录
program
  .command('auto')
  .description('Auto convert Tibco BW directory to Spring Boot (detects BWP files, schemas, and deploys)')
  .argument('<tibco-bw-dir>', 'Path to the Tibco BW directory (e.g., test/_fixtures/)')
  .option('-p, --package <name>', 'Java package name', 'com.example.movies')
  .option('--no-deploy', 'Skip deployment to Spring Boot project')
  .option('--no-validate', 'Skip API validation')
  .option('--no-test-generation', 'Skip API test code generation')
  .option('--no-app-start', 'Skip starting Spring Boot application for testing')
  .option('--port <number>', 'Port to start Spring Boot application', '8080')
  .action(async (tibcoBwDir, options) => {
    try {
      console.log('🚀 Auto Tibco BW to Spring Boot conversion...');

      // 自动检测 BWP 文件
      const bwpFile = await findBWPFile(tibcoBwDir);
      if (!bwpFile) {
        console.error('❌ No BWP file found in the specified directory');
        process.exit(1);
      }

      console.log(`📄 Found BWP file: ${bwpFile}`);

      // 调用增强的自动转换逻辑
      await performAutoConversion({
        bwpFile,
        tibcoBwDir,
        package: options.package,
        deploy: options.deploy !== false,
        validate: options.validate !== false,
        generateTests: options.testGeneration !== false,
        startApp: options.appStart !== false,
        port: parseInt(options.port)
      });

    } catch (error) {
      console.error('❌ Auto conversion failed:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

// 简化的快速转换命令
program
  .command('quick')
  .description('Quick convert BWP to Spring Boot (auto-detects schemas and deploys)')
  .argument('<bwp-file>', 'Path to the BWP file')
  .argument('[spring-boot-project]', 'Path to Spring Boot project (optional, defaults to ./spring-boilerplate)')
  .option('-p, --package <n>', 'Java package name', 'com.example.movies')
  .option('--no-deploy', 'Skip deployment to Spring Boot project')
  .option('--no-validate', 'Skip API validation')
  .action(async (bwpFile, springBootProject, options) => {
    try {
      console.log('🚀 Quick BWP to Spring Boot conversion...');

      // 自动检测和设置路径
      const inputPath = path.resolve(bwpFile);
      const projectPath = springBootProject || './spring-boilerplate';
      const outputPath = './temp-output';

      // 自动检测 schemas 目录
      const bwpDir = path.dirname(inputPath);
      const possibleSchemasPaths = [
        path.join(bwpDir, '..', 'Schemas'),
        path.join(bwpDir, '../..', 'Schemas'),
        path.join(bwpDir, 'Schemas'),
        path.join(bwpDir, '..', '..', '..', 'Schemas')
      ];

      let schemasPath = null;
      for (const schemaPath of possibleSchemasPaths) {
        if (fs.existsSync(schemaPath)) {
          schemasPath = schemaPath;
          console.log(`📁 Auto-detected schemas directory: ${schemasPath}`);
          break;
        }
      }

      // 自动检测 swagger.json
      let swaggerPath = null;
      const possibleSwaggerPaths = [
        path.join(bwpDir, '..', 'Resources', 'swagger.json'),
        path.join(bwpDir, '../..', 'Resources', 'swagger.json'),
        path.join(bwpDir, 'swagger.json'),
        path.join(bwpDir, '..', 'swagger.json')
      ];

      for (const swagger of possibleSwaggerPaths) {
        if (fs.existsSync(swagger)) {
          swaggerPath = swagger;
          console.log(`📄 Auto-detected swagger.json: ${swaggerPath}`);
          break;
        }
      }

      // 调用完整的转换逻辑
      await performConversion({
        input: inputPath,
        output: outputPath,
        package: options.package,
        schemas: schemasPath,
        springBootProject: options.deploy !== false ? projectPath : null,
        swaggerJson: swaggerPath,
        validateApi: options.validate !== false && swaggerPath !== null,
        validation: true,
        lombok: false,
        jackson: true,
        constructors: true,
        tostring: true
      });

    } catch (error) {
      console.error('❌ Quick conversion failed:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

program
  .command('convert')
  .description('Convert a BWP file to Spring Boot Java code')
  .requiredOption('-i, --input <path>', 'Path to the BWP file')
  .option('-o, --output <path>', 'Output directory for generated Java code', './generated')
  .option('-p, --package <name>', 'Java package name', 'com.example.app')
  .option('-s, --schemas <path>', 'Path to XSD schemas directory')
  .option('--spring-boot-project <path>', 'Path to Spring Boot project for deployment')
  .option('--swagger-json <path>', 'Path to TIBCO BW swagger.json for API validation')
  .option('--validate-api', 'Validate generated API against TIBCO BW swagger.json')
  .option('--no-validation', 'Disable JSR-303 validation annotations')
  .option('--lombok', 'Use Lombok annotations')
  .option('--no-jackson', 'Disable Jackson annotations')
  .option('--no-constructors', 'Skip constructor generation')
  .option('--no-tostring', 'Skip toString method generation')
  .action(async (options) => {
    await performConversion(options);
  });

// 自动查找 BWP 文件
async function findBWPFile(tibcoBwDir: string): Promise<string | null> {
  const searchPaths = [
    path.join(tibcoBwDir, '**/*.bwp'),
    path.join(tibcoBwDir, '**/Processes/**/*.bwp'),
    path.join(tibcoBwDir, '**/**/Processes/**/*.bwp')
  ];

  for (const searchPath of searchPaths) {
    const files = await findFilesWithPattern(searchPath);
    if (files.length > 0) {
      // 返回第一个找到的 BWP 文件
      return files[0];
    }
  }

  return null;
}

// 自动查找 Swagger 文件
async function findSwaggerFile(tibcoBwDir: string): Promise<string | null> {
  const possibleSwaggerPaths = [
    path.join(tibcoBwDir, 'Resources', 'swagger.json'),
    path.join(tibcoBwDir, '**/Resources/swagger.json'),
    path.join(tibcoBwDir, 'swagger.json'),
    path.join(tibcoBwDir, '**/swagger.json')
  ];

  for (const swaggerPath of possibleSwaggerPaths) {
    if (swaggerPath.includes('**')) {
      // 使用 glob 模式搜索
      const files = await findFilesWithPattern(swaggerPath);
      if (files.length > 0) {
        return files[0];
      }
    } else {
      // 直接检查文件是否存在
      if (fs.existsSync(swaggerPath)) {
        return swaggerPath;
      }
    }
  }

  return null;
}

// 简单的文件查找函数
async function findFilesWithPattern(pattern: string): Promise<string[]> {
  const { glob } = require('glob');
  try {
    const files = await glob(pattern);
    return files;
  } catch (error) {
    console.error('Error finding files:', error);
    return [];
  }
}

// 自动转换逻辑
async function performAutoConversion(options: {
  bwpFile: string;
  tibcoBwDir: string;
  package: string;
  deploy: boolean;
  validate: boolean;
  generateTests?: boolean;
  startApp?: boolean;
  port?: number;
}) {
  const bwpDir = path.dirname(options.bwpFile);
  const projectPath = './spring-boilerplate';
  const outputPath = './temp-output';

  // 自动检测 schemas 目录
  const possibleSchemasPaths = [
    path.join(options.tibcoBwDir, 'Schemas'),
    path.join(bwpDir, '..', 'Schemas'),
    path.join(bwpDir, '../..', 'Schemas'),
    path.join(bwpDir, 'Schemas'),
    path.join(bwpDir, '..', '..', '..', 'Schemas')
  ];

  let schemasPath = null;
  for (const schemaPath of possibleSchemasPaths) {
    if (fs.existsSync(schemaPath)) {
      schemasPath = schemaPath;
      console.log(`📁 Auto-detected schemas directory: ${schemasPath}`);
      break;
    }
  }

  // 自动检测 swagger.json
  let swaggerPath = await findSwaggerFile(options.tibcoBwDir);
  if (swaggerPath) {
    console.log(`📄 Auto-detected swagger.json: ${swaggerPath}`);
  }

  // 调用完整的转换逻辑
  await performConversion({
    input: options.bwpFile,
    output: outputPath,
    package: options.package,
    schemas: schemasPath,
    springBootProject: options.deploy ? projectPath : null,
    swaggerJson: swaggerPath,
    validateApi: options.validate && swaggerPath !== null,
    validation: true,
    lombok: false,
    jackson: true,
    constructors: true,
    tostring: true
  });

  // 新增：API 测试代码生成和应用启动验证
  if (options.generateTests && swaggerPath && options.deploy) {
    console.log('\n🧪 Generating API test code...');

    try {
      // 解析 Swagger 文件
      const swaggerParser = new SwaggerParser();
      const swaggerSpec = await swaggerParser.parseSwaggerFile(swaggerPath);

      const apiInfo = swaggerParser.getApiInfo();
      console.log(`📖 Parsed API: ${apiInfo.title} v${apiInfo.version}`);

      // 生成测试用例
      const testCases = swaggerParser.generateTestCases();
      console.log(`✅ Generated ${testCases.length} test cases`);

      // 生成测试代码
      const testOptions: ApiTestGenerationOptions = {
        packageName: options.package,
        outputDir: projectPath,
        testFramework: 'junit5',
        useSpringBootTest: true,
        generateIntegrationTests: true,
        generateUnitTests: true,
        includeNegativeTests: true,
        baseUrl: swaggerParser.getBaseUrl()
      };

      const testGenerator = new ApiTestGenerator(testOptions);
      const testResult = await testGenerator.generateAllTests(testCases);

      console.log(`✅ Generated test files:`);
      testResult.generatedFiles.forEach(file => {
        console.log(`   - ${path.relative(process.cwd(), file)}`);
      });

      // 启动应用进行验证（如果需要）
      if (options.startApp) {
        console.log('\n🚀 Starting Spring Boot application for testing...');

        const generationOptions: JavaGenerationOptions = {
          packageName: options.package,
          outputDir: outputPath,
          useJSR303Validation: true,
          useLombok: false,
          useJacksonAnnotations: true,
          includeConstructors: true,
          includeToString: true
        };

        const deployer = new SpringBootDeployer(projectPath, generationOptions);
        const startupResult = await deployer.startApplication({
          port: options.port || 8080,
          timeout: 60000
        });

        if (startupResult.success) {
          console.log(`✅ Application started successfully on port ${startupResult.port}`);

          // 运行健康检查
          console.log('🏥 Running health check...');
          const isHealthy = await deployer.isApplicationRunning(startupResult.port);
          if (isHealthy) {
            console.log('✅ Application health check passed');
          } else {
            console.warn('⚠️  Application health check failed');
          }

          console.log('\n🎯 Testing workflow completed! Next steps:');
          console.log(`   1. Application is running at http://localhost:${startupResult.port}`);
          console.log('   2. Run tests: mvn test or ./gradlew test');
          console.log('   3. Test API endpoints manually');
          console.log(`   4. Stop application when done (PID: ${startupResult.pid})`);
        } else {
          console.warn(`⚠️  Failed to start application: ${startupResult.error}`);
        }
      }

    } catch (testError) {
      console.warn(`⚠️  API test generation failed: ${testError instanceof Error ? testError.message : String(testError)}`);
      console.log('   Continuing with basic conversion...');
    }
  }
}

// 共享的转换逻辑
async function performConversion(options: any) {
  try {
    console.log('🚀 Starting Tibco BW to Spring Boot conversion...');
      
      // 验证输入文件
      if (!fs.existsSync(options.input)) {
        console.error(`❌ Error: BWP file not found: ${options.input}`);
        process.exit(1);
      }

      // 创建输出目录
      if (!fs.existsSync(options.output)) {
        fs.mkdirSync(options.output, { recursive: true });
        console.log(`📁 Created output directory: ${options.output}`);
      }

      // 配置生成选项
      const generationOptions: JavaGenerationOptions = {
        packageName: options.package,
        outputDir: options.output,
        useJSR303Validation: options.validation !== false,
        useLombok: options.lombok === true,
        useJacksonAnnotations: options.jackson !== false,
        includeConstructors: options.constructors !== false,
        includeToString: options.tostring !== false
      };

      console.log('⚙️  Generation options:', generationOptions);

      // 解析项目配置
      console.log('🔧 Parsing project configuration...');
      const configParser = new ConfigParser();
      const projectDir = path.dirname(options.input);
      // 查找包含 META-INF 的模块目录
      let moduleDir = projectDir;
      while (moduleDir && moduleDir !== path.dirname(moduleDir)) {
        if (fs.existsSync(path.join(moduleDir, 'META-INF'))) {
          break;
        }
        moduleDir = path.dirname(moduleDir);
      }
      console.log(`📁 Using module directory: ${moduleDir}`);
      const config = await configParser.parseProjectConfig(moduleDir);

      console.log(`✅ Parsed configuration:`);
      console.log(`   - HTTP clients: ${config.httpClients.size}`);
      console.log(`   - Global variables: ${config.globalVariables.size}`);
      console.log(`   - Service URLs: ${config.serviceUrls.size}`);

      // 解析 BWP 文件
      console.log(`📖 Parsing BWP file: ${options.input}`);
      const parser = new BWPParser();
      const parsedProcess = await parser.parseBWP(options.input);

      console.log(`✅ Successfully parsed BWP: ${parsedProcess.name}`);
      console.log(`   - Namespace: ${parsedProcess.namespace}`);
      console.log(`   - Variables: ${parsedProcess.variables.length}`);
      console.log(`   - REST endpoints: ${parsedProcess.restEndpoints.length}`);
      console.log(`   - Activities: ${parsedProcess.activities.length}`);

      // 生成 XSD 模型（如果提供了 schemas 路径）
      let modelResult;
      if (options.schemas) {
        if (!fs.existsSync(options.schemas)) {
          console.warn(`⚠️  Warning: Schemas directory not found: ${options.schemas}`);
        } else {
          console.log(`🏗️  Generating XSD models from: ${options.schemas}`);
          const modelGenerator = new XSDJavaModelGenerator(generationOptions);
          modelResult = await modelGenerator.generateAllModelsForBWP(options.schemas);
          console.log(`✅ Generated ${modelResult.allClasses.length} model classes`);
        }
      }

      // 生成 BWP Java 代码
      console.log('🏗️  Generating Java code from BWP...');
      const bwpGenerator = new BWPJavaGenerator(generationOptions, config);
      const controllerCode = bwpGenerator.generateController(parsedProcess);
      const serviceCode = bwpGenerator.generateService(parsedProcess);

      // 生成 application.properties
      console.log('🏗️  Generating application.properties...');
      const propertiesGenerator = new PropertiesGenerator();
      const propertiesContent = propertiesGenerator.generateApplicationProperties(config);

      // 写入生成的代码
      const packageDir = path.join(options.output, ...options.package.split('.'));
      fs.mkdirSync(packageDir, { recursive: true });

      const controllerFile = path.join(packageDir, `${parsedProcess.name}Controller.java`);
      const serviceFile = path.join(packageDir, `${parsedProcess.name}Service.java`);

      fs.writeFileSync(controllerFile, controllerCode);
      fs.writeFileSync(serviceFile, serviceCode);

      // 写入 application.properties
      const propertiesFile = path.join(options.output, 'application.properties');
      fs.writeFileSync(propertiesFile, propertiesContent);

      console.log(`✅ Generated controller: ${controllerFile}`);
      console.log(`✅ Generated service: ${serviceFile}`);
      console.log(`✅ Generated properties: ${propertiesFile}`);

      // 部署到 Spring Boot 项目（如果提供了路径）
      if (options.springBootProject) {
        console.log(`🚀 Deploying to Spring Boot project: ${options.springBootProject}`);
        const deployer = new SpringBootDeployer(options.springBootProject, generationOptions);
        await deployer.deployGeneratedCode(options.output);

        const isValid = await deployer.validateDeployment();
        if (isValid) {
          console.log('✅ Deployment completed successfully');

          // API 一致性验证（如果提供了 swagger.json）
          if (options.validateApi && options.swaggerJson) {
            console.log('🔍 Validating API consistency with TIBCO BW swagger.json...');

            if (!fs.existsSync(options.swaggerJson)) {
              console.warn(`⚠️  Swagger JSON file not found: ${options.swaggerJson}`);
            } else {
              const apiValidation = await deployer.validateApiConsistency(options.swaggerJson);

              if (apiValidation.isConsistent) {
                console.log('✅ API is consistent with TIBCO BW swagger.json');
              } else {
                console.warn('⚠️  API inconsistencies found:');
                apiValidation.differences.forEach(diff => {
                  console.warn(`   - ${diff}`);
                });
              }
            }
          }

          const projectInfo = deployer.getProjectInfo();
          console.log(`📦 Project type: ${projectInfo.type}`);
          console.log('🎯 Next steps:');
          console.log(`   1. cd ${options.springBootProject}`);
          console.log('   2. mvn spring-boot:run (for Maven) or ./gradlew bootRun (for Gradle)');
          console.log('   3. Test the endpoints at http://localhost:8080');

          if (options.validateApi && options.swaggerJson) {
            console.log('   4. Verify API endpoints match the swagger specification');
          }
        } else {
          console.warn('⚠️  Deployment validation failed');
        }
      }

      console.log('🎉 Conversion completed successfully!');
      
      // 显示生成的文件摘要
      console.log('\n📋 Generated files summary:');
      console.log(`   - Controller: ${path.relative(process.cwd(), controllerFile)}`);
      console.log(`   - Service: ${path.relative(process.cwd(), serviceFile)}`);
      if (modelResult) {
        console.log(`   - Model classes: ${modelResult.allClasses.length}`);
      }

    } catch (error) {
      console.error('❌ Error during conversion:', error);
      process.exit(1);
    }
}

program
  .command('validate')
  .description('Validate a BWP file without generating code')
  .requiredOption('-i, --input <path>', 'Path to the BWP file')
  .action(async (options) => {
    try {
      console.log(`🔍 Validating BWP file: ${options.input}`);
      
      if (!fs.existsSync(options.input)) {
        console.error(`❌ Error: BWP file not found: ${options.input}`);
        process.exit(1);
      }

      const parser = new BWPParser();
      const parsedProcess = await parser.parseBWP(options.input);
      
      console.log('✅ BWP file is valid!');
      console.log(`   - Process name: ${parsedProcess.name}`);
      console.log(`   - Namespace: ${parsedProcess.namespace}`);
      console.log(`   - Variables: ${parsedProcess.variables.length}`);
      console.log(`   - Partner links: ${parsedProcess.partnerLinks.length}`);
      console.log(`   - REST endpoints: ${parsedProcess.restEndpoints.length}`);
      console.log(`   - Activities: ${parsedProcess.activities.length}`);
      
      if (parsedProcess.interface) {
        console.log(`   - Input type: ${parsedProcess.interface.inputType || 'none'}`);
        console.log(`   - Output type: ${parsedProcess.interface.outputType || 'none'}`);
      }

    } catch (error) {
      console.error('❌ Validation failed:', error);
      process.exit(1);
    }
  });

program
  .command('generate-models')
  .description('Generate Java model classes from XSD schemas')
  .requiredOption('-s, --schemas <path>', 'Path to XSD schemas directory')
  .option('-o, --output <path>', 'Output directory for generated Java code', './generated')
  .option('-p, --package <name>', 'Java package name', 'com.example.app.model')
  .option('--no-validation', 'Disable JSR-303 validation annotations')
  .option('--lombok', 'Use Lombok annotations')
  .option('--no-jackson', 'Disable Jackson annotations')
  .action(async (options) => {
    try {
      console.log(`🏗️  Generating models from XSD schemas: ${options.schemas}`);
      
      if (!fs.existsSync(options.schemas)) {
        console.error(`❌ Error: Schemas directory not found: ${options.schemas}`);
        process.exit(1);
      }

      const generationOptions: JavaGenerationOptions = {
        packageName: options.package,
        outputDir: options.output,
        useJSR303Validation: options.validation !== false,
        useLombok: options.lombok === true,
        useJacksonAnnotations: options.jackson !== false,
        includeConstructors: true,
        includeToString: true
      };

      const modelGenerator = new XSDJavaModelGenerator(generationOptions);
      const result = await modelGenerator.generateAllModelsForBWP(options.schemas);
      
      console.log(`✅ Generated ${result.allClasses.length} model classes`);
      console.log(`📁 Output directory: ${options.output}`);
      
      // 显示生成的类型
      const types = Array.from(result.inputOutputMapping.keys()).slice(0, 10);
      console.log('🏷️  Generated types (first 10):');
      types.forEach(type => console.log(`   - ${type}`));
      
      if (result.inputOutputMapping.size > 10) {
        console.log(`   ... and ${result.inputOutputMapping.size - 10} more`);
      }

    } catch (error) {
      console.error('❌ Error generating models:', error);
      process.exit(1);
    }
  });

program
  .command('validate-api')
  .description('Validate deployed Spring Boot API against TIBCO BW swagger.json')
  .requiredOption('--spring-boot-project <path>', 'Path to Spring Boot project')
  .requiredOption('--swagger-json <path>', 'Path to TIBCO BW swagger.json file')
  .option('-p, --package <name>', 'Java package name', 'com.example.app')
  .action(async (options) => {
    try {
      console.log('🔍 Validating API consistency...');

      if (!fs.existsSync(options.springBootProject)) {
        console.error(`❌ Error: Spring Boot project not found: ${options.springBootProject}`);
        process.exit(1);
      }

      if (!fs.existsSync(options.swaggerJson)) {
        console.error(`❌ Error: Swagger JSON file not found: ${options.swaggerJson}`);
        process.exit(1);
      }

      const generationOptions: JavaGenerationOptions = {
        packageName: options.package,
        outputDir: './temp',
        useJSR303Validation: true,
        useLombok: false,
        useJacksonAnnotations: true,
        includeConstructors: true,
        includeToString: true
      };

      const deployer = new SpringBootDeployer(options.springBootProject, generationOptions);
      const validation = await deployer.validateApiConsistency(options.swaggerJson);

      if (validation.isConsistent) {
        console.log('✅ API is consistent with TIBCO BW swagger.json');
        console.log('🎉 All endpoints and parameters match the specification');
      } else {
        console.warn('⚠️  API inconsistencies found:');
        validation.differences.forEach(diff => {
          console.warn(`   - ${diff}`);
        });
        console.log('\n💡 Suggestions:');
        console.log('   1. Regenerate the code with the latest BWP and XSD files');
        console.log('   2. Check if the swagger.json file is up to date');
        console.log('   3. Verify the package name and controller mappings');
      }

    } catch (error) {
      console.error('❌ Error validating API:', error);
      process.exit(1);
    }
  });

// 新的 test-api 命令
program
  .command('test-api')
  .description('Generate API tests from Swagger, start Spring Boot app, and run validation')
  .argument('<tibco-bw-dir>', 'Path to the Tibco BW directory containing swagger.json')
  .option('--spring-boot-project <path>', 'Path to Spring Boot project', './spring-boilerplate')
  .option('-p, --package <name>', 'Java package name', 'com.example.movies')
  .option('--port <number>', 'Port to start Spring Boot application', '8080')
  .option('--timeout <number>', 'Application startup timeout in seconds', '60')
  .option('--no-integration-tests', 'Skip integration test generation')
  .option('--no-unit-tests', 'Skip unit test generation')
  .option('--no-start-app', 'Skip starting the Spring Boot application')
  .action(async (tibcoBwDir, options) => {
    try {
      console.log('🚀 Starting API testing workflow...');

      // 1. 查找 swagger.json 文件
      const swaggerPath = await findSwaggerFile(tibcoBwDir);
      if (!swaggerPath) {
        console.error('❌ No swagger.json file found in the specified directory');
        process.exit(1);
      }

      console.log(`📄 Found swagger.json: ${swaggerPath}`);

      // 2. 解析 Swagger 文件
      console.log('📖 Parsing Swagger specification...');
      const swaggerParser = new SwaggerParser();
      const swaggerSpec = await swaggerParser.parseSwaggerFile(swaggerPath);

      const apiInfo = swaggerParser.getApiInfo();
      console.log(`✅ Parsed API: ${apiInfo.title} v${apiInfo.version}`);

      // 3. 生成测试用例
      console.log('🏗️  Generating API test cases...');
      const testCases = swaggerParser.generateTestCases();
      console.log(`✅ Generated ${testCases.length} test cases`);

      // 4. 生成测试代码
      if (options.integrationTests !== false || options.unitTests !== false) {
        console.log('🏗️  Generating test code...');

        const testOptions: ApiTestGenerationOptions = {
          packageName: options.package,
          outputDir: options.springBootProject,
          testFramework: 'junit5',
          useSpringBootTest: true,
          generateIntegrationTests: options.integrationTests !== false,
          generateUnitTests: options.unitTests !== false,
          includeNegativeTests: true,
          baseUrl: swaggerParser.getBaseUrl()
        };

        const testGenerator = new ApiTestGenerator(testOptions);
        const testResult = await testGenerator.generateAllTests(testCases);

        console.log(`✅ Generated test files:`);
        testResult.generatedFiles.forEach(file => {
          console.log(`   - ${path.relative(process.cwd(), file)}`);
        });
      }

      // 5. 启动 Spring Boot 应用（如果需要）
      if (options.startApp !== false) {
        console.log('🚀 Starting Spring Boot application...');

        const generationOptions: JavaGenerationOptions = {
          packageName: options.package,
          outputDir: './temp',
          useJSR303Validation: true,
          useLombok: false,
          useJacksonAnnotations: true,
          includeConstructors: true,
          includeToString: true
        };

        const deployer = new SpringBootDeployer(options.springBootProject, generationOptions);
        const startupResult = await deployer.startApplication({
          port: parseInt(options.port),
          timeout: parseInt(options.timeout) * 1000
        });

        if (startupResult.success) {
          console.log(`✅ Spring Boot application started on port ${startupResult.port}`);

          // 6. 验证 API 一致性
          console.log('🔍 Validating API consistency...');
          const validation = await deployer.validateApiConsistency(swaggerPath);

          if (validation.isConsistent) {
            console.log('✅ API is consistent with Swagger specification');
          } else {
            console.warn('⚠️  API inconsistencies found:');
            validation.differences.forEach(diff => {
              console.warn(`   - ${diff}`);
            });
          }

          // 7. 运行基本健康检查
          console.log('🏥 Running health check...');
          const isHealthy = await deployer.isApplicationRunning(startupResult.port);
          if (isHealthy) {
            console.log('✅ Application health check passed');
          } else {
            console.warn('⚠️  Application health check failed');
          }

          console.log('\n🎯 Next steps:');
          console.log(`   1. Application is running at http://localhost:${startupResult.port}`);
          console.log('   2. Run the generated tests with: mvn test or ./gradlew test');
          console.log('   3. Test the API endpoints manually or with Postman');
          console.log(`   4. Stop the application when done (PID: ${startupResult.pid})`);

        } else {
          console.error(`❌ Failed to start Spring Boot application: ${startupResult.error}`);
          if (startupResult.logs && startupResult.logs.length > 0) {
            console.log('\n📋 Application logs:');
            startupResult.logs.slice(-10).forEach(log => console.log(log));
          }
          process.exit(1);
        }
      }

      console.log('🎉 API testing workflow completed successfully!');

    } catch (error) {
      console.error('❌ API testing workflow failed:', error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

// 解析命令行参数
program.parse();
