package com.example.movies;

import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
public class SortMoviesController {
        private static final Logger logger = LoggerFactory.getLogger(SortMoviesController.class);

    @Autowired
    private SortMoviesService sortMoviesService;
        @GetMapping("/")
    public ResponseEntity<Movie> get(@RequestParam("i") String i, @RequestParam("apikey") String apikey) {
        try {
            Movie result = sortMoviesService.get(i, apikey);
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error in get: " + e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}