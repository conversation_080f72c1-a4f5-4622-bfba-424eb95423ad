package com.example.movies;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class SearchMoviesService {
    private static final Logger logger = LoggerFactory.getLogger(SearchMoviesService.class);
        @Autowired
    private RestTemplate restTemplate;

    @Value("${search.service.url}")
    private String searchServiceUrl;
        public OMDBSearchElement get(String searchString) {
        logger.info("Executing get with parameters: {}", searchString);

        try {
            OMDBSearchElement result = restTemplate.getForObject(searchServiceUrl + "/movies?" + "searchString=" + searchString, OMDBSearchElement.class);

            logger.info("Successfully completed get");
            return result;
        } catch (Exception e) {
            logger.error("Error in get: " + e.getMessage(), e);
            throw new RuntimeException("Service call failed", e);
        }
    }
}