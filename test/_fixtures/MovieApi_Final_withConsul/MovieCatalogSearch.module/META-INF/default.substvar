<?xml version = "1.0" encoding = "UTF-8"?>
<repository xmlns:xsi = "http://www.w3.org/2001/XMLSchema-instance" xmlns = "http://www.tibco.com/xmlns/repo/types/2002">
	<globalVariables>
		<globalVariable>
			<name>BW.APPNODE.NAME</name>
			<value></value>
			<deploymentSettable>false</deploymentSettable>
			<serviceSettable>false</serviceSettable>
			<type>String</type>
			<isOverride>false</isOverride>
		</globalVariable>
		<globalVariable>
			<name>BW.DEPLOYMENTUNIT.NAME</name>
			<value></value>
			<deploymentSettable>false</deploymentSettable>
			<serviceSettable>false</serviceSettable>
			<type>String</type>
			<isOverride>false</isOverride>
		</globalVariable>
		<globalVariable>
			<name>BW.HOST.NAME</name>
			<value>localhost</value>
			<deploymentSettable>false</deploymentSettable>
			<serviceSettable>false</serviceSettable>
			<type>String</type>
			<isOverride>false</isOverride>
		</globalVariable>
		<globalVariable>
			<name>BW.DEPLOYMENTUNIT.VERSION</name>
			<value></value>
			<deploymentSettable>false</deploymentSettable>
			<serviceSettable>false</serviceSettable>
			<type>String</type>
			<isOverride>false</isOverride>
		</globalVariable>
		<globalVariable>
			<name>BW.MODULE.VERSION</name>
			<value></value>
			<deploymentSettable>false</deploymentSettable>
			<serviceSettable>false</serviceSettable>
			<type>String</type>
			<isOverride>false</isOverride>
		</globalVariable>
		<globalVariable>
			<name>BW.CLOUD.PORT</name>
			<value>8080</value>
			<deploymentSettable>false</deploymentSettable>
			<serviceSettable>false</serviceSettable>
			<type>Integer</type>
			<isOverride>false</isOverride>
		</globalVariable>
		<globalVariable>
			<name>BW.MODULE.NAME</name>
			<value></value>
			<deploymentSettable>false</deploymentSettable>
			<serviceSettable>false</serviceSettable>
			<type>String</type>
			<isOverride>false</isOverride>
		</globalVariable>
		<globalVariable>
			<name>SearchServiceHost</name>
			<value>localhost</value>
			<deploymentSettable>false</deploymentSettable>
			<serviceSettable>false</serviceSettable>
			<type>String</type>
			<isOverride>false</isOverride>
		</globalVariable>
		<globalVariable>
			<name>SearchServicePort</name>
			<value>8080</value>
			<deploymentSettable>false</deploymentSettable>
			<serviceSettable>false</serviceSettable>
			<type>Integer</type>
			<isOverride>false</isOverride>
		</globalVariable>
		<globalVariable>
			<name>ServicePort</name>
			<value>9090</value>
			<deploymentSettable>false</deploymentSettable>
			<serviceSettable>false</serviceSettable>
			<type>Integer</type>
			<isOverride>false</isOverride>
		</globalVariable>
		<globalVariable>
			<name>apikey</name>
			<value>62eec860</value>
			<deploymentSettable>false</deploymentSettable>
			<serviceSettable>false</serviceSettable>
			<type>String</type>
			<isOverride>false</isOverride>
		</globalVariable>
		<globalVariable>
			<name>DetailsServiceHost</name>
			<value>www.omdbapi.com</value>
			<deploymentSettable>false</deploymentSettable>
			<serviceSettable>false</serviceSettable>
			<type>String</type>
			<isOverride>false</isOverride>
		</globalVariable>
		<globalVariable>
			<name>DetailsServicePort</name>
			<value>80</value>
			<deploymentSettable>false</deploymentSettable>
			<serviceSettable>false</serviceSettable>
			<type>Integer</type>
			<isOverride>false</isOverride>
		</globalVariable>
	</globalVariables>
</repository>
