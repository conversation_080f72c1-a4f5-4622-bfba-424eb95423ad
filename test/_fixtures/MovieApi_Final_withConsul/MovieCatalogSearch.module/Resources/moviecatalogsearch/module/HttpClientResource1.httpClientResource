<?xml version="1.0" encoding="UTF-8"?>
<jndi:namedResource xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:http="http://xsd.tns.tibco.com/bw/models/sharedresource/httpclient" xmlns:jndi="http://xsd.tns.tibco.com/amf/models/sharedresource/jndi" xmi:id="_l53Yoaz1EemnEIz6kVJMoQ" name="moviecatalogsearch.module.HttpClientResource1" type="http:HttpClientConfiguration">
  <jndi:configuration xsi:type="http:HttpClientConfiguration" xmi:id="_l53Yoqz1EemnEIz6kVJMoQ" httpClientVersion="httpcomponents" cmdExecutionIsolationStrategy="THREAD" cmdExecutionIsolationTimeout="1000" cmdExecutionTimeoutEnabled="true" cmdExecutionIsolationInterruptOnTimeout="true" cmdExecutionIsolationSemaphoreMaxConcRequests="8" cmdCircuitBreakerRequestVolumeThreshold="20" cmdCircuitBreakerSleepWindow="5000" cmdCircuitBreakerErrorThresholdPercentage="50" cmdMetricsRollingStatsTime="10000" cmdMetricsRollingStatsNumBuckets="10" cmdMetricsHealthSnapshotInterval="500" cmdRequestLogEnabled="true" tpCoreSize="10" tpMaxQueueSize="-1" tpQueueSizeRejectionThreshold="5" tpKeepAliveTime="1">
    <tcpDetails xmi:id="_l53Yo6z1EemnEIz6kVJMoQ">
      <substitutionBindings xmi:id="_03HwMKz3EemnEIz6kVJMoQ" template="host" propName="DetailsServiceHost"/>
      <substitutionBindings xmi:id="_12b4IKz3EemnEIz6kVJMoQ" template="port" propName="DetailsServicePort"/>
    </tcpDetails>
  </jndi:configuration>
</jndi:namedResource>
