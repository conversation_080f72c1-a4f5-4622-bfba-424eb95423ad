<?xml version="1.0" encoding="UTF-8"?>
<jndi:namedResource xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:http="http://xsd.tns.tibco.com/bw/models/sharedresource/httpclient" xmlns:jndi="http://xsd.tns.tibco.com/amf/models/sharedresource/jndi" xmi:id="_Yur1kayZEemnEIz6kVJMoQ" name="moviecatalogsearch.module.HttpClientResource" type="http:HttpClientConfiguration">
  <jndi:configuration xsi:type="http:HttpClientConfiguration" xmi:id="_YuscoKyZEemnEIz6kVJMoQ" httpClientVersion="httpcomponents" cmdExecutionIsolationStrategy="THREAD" cmdExecutionIsolationTimeout="1000" cmdExecutionTimeoutEnabled="true" cmdExecutionIsolationInterruptOnTimeout="true" cmdExecutionIsolationSemaphoreMaxConcRequests="8" cmdCircuitBreakerRequestVolumeThreshold="20" cmdCircuitBreakerSleepWindow="5000" cmdCircuitBreakerErrorThresholdPercentage="50" cmdMetricsRollingStatsTime="10000" cmdMetricsRollingStatsNumBuckets="10" cmdMetricsHealthSnapshotInterval="500" cmdRequestLogEnabled="true" tpCoreSize="10" tpMaxQueueSize="-1" tpQueueSizeRejectionThreshold="5" tpKeepAliveTime="1">
    <tcpDetails xmi:id="_YuscoayZEemnEIz6kVJMoQ" host="SearchServiceHost localhost">
      <substitutionBindings xmi:id="_OngagKz3EemnEIz6kVJMoQ" template="host" propName="SearchServiceHost"/>
      <substitutionBindings xmi:id="_SqoRsKz3EemnEIz6kVJMoQ" template="port" propName="SearchServicePort"/>
    </tcpDetails>
  </jndi:configuration>
</jndi:namedResource>
