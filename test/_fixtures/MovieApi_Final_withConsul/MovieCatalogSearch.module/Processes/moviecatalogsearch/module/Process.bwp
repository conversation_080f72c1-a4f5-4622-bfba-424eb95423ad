<?xml version="1.0" encoding="UTF-8"?>
<bpws:process exitOnStandardFault="no"
    name="moviecatalogsearch.module.Process" suppressJoinFailure="yes"
    targetNamespace="http://xmlns.example.com/20190722200552"
    xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
    xmlns:info="http://www.tibco.com/bw/process/info"
    xmlns:ns="http://www.tibco.com/pe/EngineTypes"
    xmlns:ns0="http://xmlns.example.com/20190722200552PLT"
    xmlns:ns1="http://xmlns.example.com/SearchMovies/parameters"
    xmlns:ns2="http://www.example.org/MovieCatalogMaster"
    xmlns:sca="http://docs.oasis-open.org/ns/opencsa/sca/200912"
    xmlns:sca-bpel="http://docs.oasis-open.org/ns/opencsa/sca-bpel/200801"
    xmlns:tibex="http://www.tibco.com/bpel/2007/extensions"
    xmlns:tibprop="http://ns.tibco.com/bw/property" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <tibex:Types>
        <xs:schema attributeFormDefault="unqualified"
            elementFormDefault="qualified"
            targetNamespace="http://www.tibco.com/pe/EngineTypes"
            xmlns:tns="http://www.tibco.com/pe/EngineTypes" xmlns:xs="http://www.w3.org/2001/XMLSchema">
            <xs:complexType block="extension restriction"
                final="extension restriction" name="ErrorReport">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="StackTrace" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Msg" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="FullClass" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Class" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ProcessStack" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="MsgCode" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Data" type="tns:anydata"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="OptionalErrorReport">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="StackTrace" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Msg" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="FullClass" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Class" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="ProcessStack" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="MsgCode" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Data" type="tns:anydata"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="FaultDetail">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ActivityName" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Data" type="tns:anydata"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Msg" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="MsgCode" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ProcessStack" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="StackTrace" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="FullClass" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Class" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="ProcessContext">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="JobId" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ApplicationName" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="EngineName" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ProcessInstanceId" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="CustomJobId" type="xs:string"/>
                    <!--xs:element name="RestartedFromCheckpoint" form="unqualified" block="extension restriction substitution" type="xs:boolean"/-->
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" maxOccurs="unbounded"
                        minOccurs="0" name="TrackingInfo" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="anydata">
                <xs:sequence>
                    <xs:any namespace="##any" processContents="lax"/>
                </xs:sequence>
            </xs:complexType>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="OptionalErrorReport" type="tns:OptionalErrorReport"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="ErrorReport" type="tns:ErrorReport"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="FaultDetail" type="tns:FaultDetail"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="ProcessContext" type="tns:ProcessContext"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="CorrelationValue" type="xs:string"/>
        </xs:schema>
        <schema attributeFormDefault="unqualified"
            elementFormDefault="unqualified"
            targetNamespace="http://schemas.tibco.com/bw/pe/plugin/5.0/exceptions"
            version="" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.tibco.com/bw/pe/plugin/5.0/exceptions">
            <complexType name="ActivityExceptionType">
                <sequence>
                    <element name="msg" type="string"/>
                    <element minOccurs="0" name="msgCode" type="string"/>
                </sequence>
            </complexType>
            <element name="ActivityException" type="tns:ActivityExceptionType"/>
            <complexType name="ActivityTimedOutExceptionType">
                <complexContent>
                    <extension base="tns:ActivityExceptionType"/>
                </complexContent>
            </complexType>
            <element name="ActivityTimedOutException" type="tns:ActivityTimedOutExceptionType"/>
            <complexType name="DuplicateKeyExceptionType">
                <complexContent>
                    <extension base="tns:ActivityExceptionType">
                        <sequence>
                            <element name="duplicateKey" type="string"/>
                            <element minOccurs="0" name="previousJobID" type="string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>
            <element name="DuplicateKeyException" type="tns:DuplicateKeyExceptionType"/>
        </schema>
        <wsdl:definitions
            targetNamespace="http://xmlns.example.com/20190722200552PLT"
            xmlns:extns="http://www.example.org/MovieCatalogMaster"
            xmlns:extns1="http://tns.tibco.com/bw/REST"
            xmlns:extns2="http://xmlns.example.com/Movies/parameters"
            xmlns:plnk="http://docs.oasis-open.org/wsbpel/2.0/plnktype"
            xmlns:tibex="http://www.tibco.com/bpel/2007/extensions"
            xmlns:tns="http://xmlns.example.com/20190722200552PLT"
            xmlns:vprop="http://docs.oasis-open.org/wsbpel/2.0/varprop"
            xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <plnk:partnerLinkType name="partnerLinkType">
                <plnk:role name="use" portType="tns:movies"/>
            </plnk:partnerLinkType>
            <wsdl:import namespace="http://www.example.org/MovieCatalogMaster"/>
            <wsdl:import namespace="http://tns.tibco.com/bw/REST"/>
            <wsdl:import namespace="http://xmlns.example.com/Movies/parameters"/>
            <wsdl:message name="getRequest">
                <wsdl:part element="extns1:httpHeaders"
                    name="httpHeaders" tibex:source="bw.rest"/>
                <wsdl:part element="extns2:moviesGetParameters"
                    name="parameters" tibex:source="bw.rest"/>
            </wsdl:message>
            <wsdl:message name="getResponse">
                <wsdl:part element="extns:Movie" name="item" tibex:hasMultipleNamespaces="false"/>
            </wsdl:message>
            <wsdl:message name="get4XXFaultMessage">
                <wsdl:part element="extns1:client4XXError" name="clientError"/>
            </wsdl:message>
            <wsdl:message name="get5XXFaultMessage">
                <wsdl:part element="extns1:server5XXError" name="serverError"/>
            </wsdl:message>
            <wsdl:portType name="movies" tibex:bw.rest.apipath="/movies"
                tibex:bw.rest.basepath="Movies"
                tibex:bw.rest.resource="Service Descriptors/moviecatalogsearch.module.Process-Movies.json"
                tibex:bw.rest.resource.source="generated" tibex:source="bw.rest.service">
                <wsdl:documentation>Summary about the new REST service.</wsdl:documentation>
                <wsdl:operation name="get">
                    <wsdl:input message="tns:getRequest" name="getInput"/>
                    <wsdl:output message="tns:getResponse" name="getOutput"/>
                    <wsdl:fault message="tns:get4XXFaultMessage" name="clientFault"/>
                    <wsdl:fault message="tns:get5XXFaultMessage" name="serverFault"/>
                </wsdl:operation>
            </wsdl:portType>
        </wsdl:definitions>
    </tibex:Types>
    <tibex:ProcessInfo callable="false" createdBy="awagle"
        createdOn="Mon Jul 22 20:05:52 IST 2019" description=""
        extraErrorVars="true" modifiers="public"
        productVersion="2.4.4 V34 2019-05-22" scalable="true"
        singleton="true" stateless="true" type="IT"/>
    <tibex:ProcessInterface context="" input="" output=""/>
    <tibex:ProcessTemplateConfigurations/>
    <notation:Diagram measurementUnit="Pixel" type="BWProcess"
        xmlns:bwnotation="http://tns.tibco.com/bw/runtime/BWNotation"
        xmlns:notation="http://www.eclipse.org/gmf/runtime/1.0.2/notation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <children type="2001">
            <children type="5001"/>
            <children type="3001">
                <styles xsi:type="notation:SortingStyle"/>
                <styles xsi:type="notation:FilteringStyle"/>
                <element href="//0/@process"/>
            </children>
            <children type="3002">
                <styles xsi:type="notation:SortingStyle"/>
                <styles xsi:type="notation:FilteringStyle"/>
            </children>
            <children type="3003">
                <styles xsi:type="notation:SortingStyle"/>
                <styles xsi:type="notation:FilteringStyle"/>
            </children>
            <children type="3004">
                <children type="4018">
                    <children type="3018">
                        <children type="4020">
                            <children type="3020">
                                <children type="4005">
                                    <children type="3007">
                                    <children type="4010">
                                    <children type="5002"/>
                                    <children type="3010">
                                    <children type="4013">
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.0"/>
                                    <layoutConstraint
                                    height="34"
                                    width="34" x="9"
                                    xsi:type="notation:Bounds" y="55"/>
                                    </children>
                                    <styles xsi:type="notation:SortingStyle"/>
                                    <styles xsi:type="notation:FilteringStyle"/>
                                    </children>
                                    <children type="3011">
                                    <children type="4011">
                                    <children type="3012">
                                    <children type="4002">
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.0/@messages.0/@activity/@activity/@activities.0"/>

                                    <layoutConstraint
                                    xsi:type="notation:Bounds" y="40"/>
                                    </children>
                                    <styles xsi:type="notation:SortingStyle"/>
                                    <styles xsi:type="notation:FilteringStyle"/>
                                    </children>
                                    <children type="3013">
                                    <children type="4018">
                                    <children type="3018">

                                    <children type="4020">

                                    <children type="3020">

                                    <children type="4005">

                                    <children type="3007">

                                    <children type="4002">

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>

                                    <element href="//0/@process/@activity/@activity/@activities.0/@messages.0/@activity/@activity/@activities.2"/>

                                    <layoutConstraint
                                    x="387"
                                    xsi:type="notation:Bounds" y="11"/>
                                    </children>

                                    <children type="4002">

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>

                                    <element href="//0/@process/@activity/@activity/@activities.0/@messages.0/@activity/@activity/@activities.3"/>

                                    <layoutConstraint
                                    x="53"
                                    xsi:type="notation:Bounds" y="11"/>
                                    </children>

                                    <children type="4002">

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>

                                    <element href="//0/@process/@activity/@activity/@activities.0/@messages.0/@activity/@activity/@activities.4"/>

                                    <layoutConstraint
                                    x="205"
                                    xsi:type="notation:Bounds" y="11"/>
                                    </children>

                                    <styles xsi:type="notation:DrawerStyle"/>

                                    <styles xsi:type="notation:SortingStyle"/>

                                    <styles xsi:type="notation:FilteringStyle"/>
                                    </children>

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>

                                    <styles xsi:type="bwnotation:ResizingStyle"/>

                                    <element href="//0/@process/@activity/@activity/@activities.0/@messages.0/@activity/@activity"/>

                                    <layoutConstraint
                                    height="99"
                                    width="587" xsi:type="notation:Bounds"/>
                                    </children>

                                    <styles xsi:type="notation:SortingStyle"/>

                                    <styles xsi:type="notation:FilteringStyle"/>
                                    </children>

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <styles xsi:type="bwnotation:BackgroundStyle"/>

                                    <styles xsi:type="bwnotation:ResizingStyle"/>

                                    <element href="//0/@process/@activity/@activity/@activities.0/@messages.0/@activity"/>

                                    <layoutConstraint
                                    height="-45"
                                    width="-76"
                                    x="30"
                                    xsi:type="notation:Bounds" y="20"/>
                                    </children>
                                    <styles xsi:type="notation:SortingStyle"/>
                                    <styles xsi:type="notation:FilteringStyle"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles xsi:type="bwnotation:BackgroundStyle"/>
                                    <styles xsi:type="bwnotation:ResizingStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.0/@messages.0/@activity"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles xsi:type="notation:SortingStyle"/>
                                    <styles xsi:type="notation:FilteringStyle"/>
                                    </children>
                                    <children type="3016">
                                    <children type="4002">
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.0/@messages.0/@activity/@activity/@activities.1"/>

                                    <layoutConstraint
                                    xsi:type="notation:Bounds" y="40"/>
                                    </children>
                                    <styles xsi:type="notation:SortingStyle"/>
                                    <styles xsi:type="notation:FilteringStyle"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles xsi:type="bwnotation:BackgroundStyle"/>
                                    <styles xsi:type="bwnotation:ResizingStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.0/@messages.0"/>
                                    <layoutConstraint
                                    height="140"
                                    width="485" xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles xsi:type="notation:SortingStyle"/>
                                    <styles xsi:type="notation:FilteringStyle"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles xsi:type="bwnotation:BackgroundStyle"/>
                                    <styles xsi:type="bwnotation:ResizingStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.0"/>
                                    <layoutConstraint
                                    height="172" width="711" xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles xsi:type="notation:DrawerStyle"/>
                                    <styles xsi:type="notation:SortingStyle"/>
                                    <styles xsi:type="notation:FilteringStyle"/>
                                    <element href="//0/@process/@activity/@activity"/>
                                    </children>
                                    <styles fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <styles xsi:type="bwnotation:ResizingStyle"/>
                                    <element href="//0/@process/@activity/@activity"/>
                                    <layoutConstraint height="384"
                                    width="721" xsi:type="notation:Bounds"/>
                                </children>
                                <styles xsi:type="notation:SortingStyle"/>
                                <styles xsi:type="notation:FilteringStyle"/>
                            </children>
                            <styles fontName=".SF NS Text" lineColor="0" xsi:type="notation:ShapeStyle"/>
                            <styles xsi:type="bwnotation:BackgroundStyle"/>
                            <styles xsi:type="bwnotation:ResizingStyle"/>
                            <element href="//0/@process/@activity"/>
                            <layoutConstraint height="384" width="724" xsi:type="notation:Bounds"/>
                        </children>
                        <styles xsi:type="notation:SortingStyle"/>
                        <styles xsi:type="notation:FilteringStyle"/>
                        <element href="//0/@process/@activity"/>
                    </children>
                    <styles fontName=".SF NS Text" lineColor="0" xsi:type="notation:ShapeStyle"/>
                    <styles xsi:type="bwnotation:BackgroundStyle"/>
                    <styles xsi:type="bwnotation:ResizingStyle"/>
                    <element href="//0/@process/@activity"/>
                    <layoutConstraint height="408" width="724" x="1"
                        xsi:type="notation:Bounds" y="1"/>
                </children>
                <styles xsi:type="notation:SortingStyle"/>
                <styles xsi:type="notation:FilteringStyle"/>
                <element href="//0/@process"/>
            </children>
            <styles fontName=".SF NS Text" lineColor="0" xsi:type="notation:ShapeStyle"/>
            <element href="//0/@process"/>
            <layoutConstraint height="460" width="1066" xsi:type="notation:Bounds"/>
        </children>
        <styles xsi:type="notation:DiagramStyle"/>
        <element href="//0"/>
        <edges
            source="//@children.0/@children.4/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2/@children.0/@children.1/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.1"
            target="//@children.0/@children.4/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2/@children.0/@children.1/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2" type="4006">
            <children type="6002">
                <layoutConstraint xsi:type="notation:Location" y="40"/>
            </children>
            <styles lineColor="0" xsi:type="notation:ConnectorStyle"/>
            <styles fontName=".SF NS Text" xsi:type="notation:FontStyle"/>
            <element href="//0/@process/@activity/@activity/@activities.0/@messages.0/@activity/@activity/@links/@children.0"/>
            <bendpoints points="[25, 0, -127, 0]$[128, 0, -24, 0]" xsi:type="notation:RelativeBendpoints"/>
        </edges>
        <edges
            source="//@children.0/@children.4/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2/@children.0/@children.1/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2"
            target="//@children.0/@children.4/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2/@children.0/@children.1/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0" type="4006">
            <children type="6002">
                <layoutConstraint xsi:type="notation:Location" y="40"/>
            </children>
            <styles lineColor="0" xsi:type="notation:ConnectorStyle"/>
            <styles fontName=".SF NS Text" xsi:type="notation:FontStyle"/>
            <element href="//0/@process/@activity/@activity/@activities.0/@messages.0/@activity/@activity/@links/@children.1"/>
            <bendpoints points="[25, 0, -157, 0]$[158, 0, -24, 0]" xsi:type="notation:RelativeBendpoints"/>
        </edges>
    </notation:Diagram>
    <tibex:NamespaceRegistry enabled="true">
        <tibex:namespaceItem
            namespace="http://xmlns.example.com/20190722200552PLT" prefix="tns"/>
        <tibex:namespaceItem
            namespace="http://xmlns.example.com/SearchMovies/parameters" prefix="tns1"/>
    </tibex:NamespaceRegistry>
    <bpws:import importType="http://www.w3.org/2001/XMLSchema" namespace="http://www.example.org/MovieCatalogMaster"/>
    <bpws:import importType="http://www.w3.org/2001/XMLSchema" namespace="http://tns.tibco.com/bw/REST"/>
    <bpws:import importType="http://www.w3.org/2001/XMLSchema" namespace="http://xmlns.example.com/SearchMovies/parameters"/>
    <bpws:import importType="http://www.w3.org/2001/XMLSchema" namespace="http://xmlns.example.com/Movies/parameters"/>
    <bpws:partnerLinks>
        <bpws:partnerLink myRole="use" name="movies"
            partnerLinkType="ns0:partnerLinkType"
            sca-bpel:ignore="false" sca-bpel:service="movies"/>
    </bpws:partnerLinks>
    <bpws:variables>
        <bpws:variable element="ns:ProcessContext"
            name="_processContext" sca-bpel:internal="true"/>
        <bpws:variable messageType="ns0:getRequest" name="get" sca-bpel:internal="true"/>
        <bpws:variable messageType="ns0:getResponse" name="getOut-input" sca-bpel:internal="true"/>
        <bpws:variable element="ns1:moviesGetParameters"
            name="SearchMovies-input" sca-bpel:internal="true"/>
        <bpws:variable element="ns2:OMDBSearchElement"
            name="SearchMovies" sca-bpel:internal="true"/>
        <bpws:variable element="ns2:OMDBSearchElement"
            name="SortMovies-input" sca-bpel:internal="true"/>
        <bpws:variable element="ns2:Movie" name="SortMovies" sca-bpel:internal="true"/>
    </bpws:variables>
    <bpws:extensions>
        <bpws:extension mustUnderstand="no" namespace="http://www.eclipse.org/gmf/runtime/1.0.2/notation"/>
        <bpws:extension mustUnderstand="no" namespace="http://www.tibco.com/bw/process/info"/>
        <bpws:extension mustUnderstand="no" namespace="http://docs.oasis-open.org/ns/opencsa/sca-bpel/200801"/>
        <bpws:extension mustUnderstand="no" namespace="http://docs.oasis-open.org/ns/opencsa/sca/200912"/>
        <bpws:extension mustUnderstand="no" namespace="http://ns.tibco.com/bw/property"/>
        <bpws:extension mustUnderstand="no" namespace="http://www.tibco.com/bpel/2007/extensions"/>
    </bpws:extensions>
    <bpws:scope name="scope">
        <bpws:flow name="flow">
            <bpws:links/>
            <bpws:pick createInstance="yes" name="pick" tibex:xpdlId="19cc5247-0121-4b56-aaf4-384360bbb6e5">
                <bpws:onMessage operation="get" partnerLink="movies"
                    portType="ns0:movies"
                    tibex:xpdlId="a2d9ffe9-6ebc-4db3-bd30-2df0925fa4e6" variable="get">
                    <bpws:scope name="scope1">
                        <bpws:flow name="flow1">
                            <bpws:links>
                                <bpws:link
                                    name="SearchMoviesToSortMovies" tibex:linkType="SUCCESS"/>
                                <bpws:link name="SortMoviesTogetOut" tibex:linkType="SUCCESS"/>
                            </bpws:links>
                            <bpws:empty name="OnMessageStart"
                                tibex:constructor="onMessageStart" tibex:xpdlId="92ccc9ed-75b4-47a5-bd78-cb647106421c"/>
                            <bpws:empty name="OnMessageEnd"
                                tibex:constructor="onMessageEnd" tibex:xpdlId="66d2cebb-01e6-41ea-b435-4f0dde1ebda6"/>
                            <bpws:reply name="getOut" operation="get"
                                partnerLink="movies"
                                portType="ns0:movies"
                                tibex:xpdlId="50c9e364-e73e-478a-b500-0fa563cbc8f9" variable="getOut-input">
                                <tibex:inputBindings>
                                    <tibex:partBinding
                                    expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns=&quot;http://xmlns.example.com/20190722200552PLT&quot; version=&quot;2.0&quot;>&#xa;    &lt;xsl:param name=&quot;SortMovies&quot;/>&#xa;    &lt;xsl:template name=&quot;getOut-input&quot; match=&quot;/&quot;>&#xa;        &lt;xsl:copy-of select=&quot;$SortMovies&quot;/>&#xa;    &lt;/xsl:template>&#xa;&lt;/xsl:stylesheet>" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"/>
                                </tibex:inputBindings>
                                <tibex:inputBinding expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0">&lt;?xml version="1.0" encoding="UTF-8"?&gt;
&lt;xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:tns="http://xmlns.example.com/20190722200552PLT" version="2.0"&gt;&lt;xsl:param name="SortMovies"/&gt;&lt;xsl:template name="getOut-input" match="/"&gt;&lt;tns:getResponse&gt;&lt;item&gt;&lt;xsl:copy-of select="$SortMovies"/&gt;&lt;/item&gt;&lt;/tns:getResponse&gt;&lt;/xsl:template&gt;&lt;/xsl:stylesheet&gt;</tibex:inputBinding>
                                <bpws:targets>
                                    <bpws:target linkName="SortMoviesTogetOut"/>
                                </bpws:targets>
                            </bpws:reply>
                            <bpws:extensionActivity>
                                <tibex:extActivity
                                    expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns=&quot;http://xmlns.example.com/Movies/parameters&quot; xmlns:tns1=&quot;http://xmlns.example.com/SearchMovies/parameters&quot; version=&quot;2.0&quot;>&lt;xsl:param name=&quot;get&quot;/>&lt;xsl:template name=&quot;SearchMovies-input&quot; match=&quot;/&quot;>&lt;tns1:moviesGetParameters>&lt;tns1:searchString>&lt;xsl:value-of select=&quot;$get/parameters/tns:moviesGetParameters/tns:searchString&quot;/>&lt;/tns1:searchString>&lt;/tns1:moviesGetParameters>&lt;/xsl:template>&lt;/xsl:stylesheet>"
                                    expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"
                                    inputVariable="SearchMovies-input"
                                    name="SearchMovies"
                                    outputVariable="SearchMovies"
                                    tibex:xpdlId="e904295d-357a-4e65-9f61-4b5eea4886a9"
                                    type="bw.generalactivities.callprocess" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
                                    <bpws:sources>
                                    <bpws:source linkName="SearchMoviesToSortMovies"/>
                                    </bpws:sources>
                                    <tibex:inputBindings>
                                    <tibex:inputBinding
                                    expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns=&quot;http://xmlns.example.com/Movies/parameters&quot; xmlns:tns1=&quot;http://xmlns.example.com/SearchMovies/parameters&quot; version=&quot;2.0&quot;>&#xa;    &lt;xsl:param name=&quot;get.parameters&quot;/>&#xa;    &lt;xsl:template name=&quot;SearchMovies-input&quot; match=&quot;/&quot;>&#xa;        &lt;tns1:moviesGetParameters>&#xa;            &lt;tns1:searchString>&#xa;                &lt;xsl:value-of select=&quot;$get.parameters/tns:searchString&quot;/>&#xa;            &lt;/tns1:searchString>&#xa;        &lt;/tns1:moviesGetParameters>&#xa;    &lt;/xsl:template>&#xa;&lt;/xsl:stylesheet>" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"/>
                                    </tibex:inputBindings>
                                    <tibex:CallProcess
                                    subProcessName="moviecatalogsearch.module.SearchMovies" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions"/>
                                </tibex:extActivity>
                            </bpws:extensionActivity>
                            <bpws:extensionActivity>
                                <tibex:extActivity
                                    inputVariable="SortMovies-input"
                                    name="SortMovies"
                                    outputVariable="SortMovies"
                                    tibex:copyOf="SearchMovies"
                                    tibex:xpdlId="835ffad3-9216-4d1e-8aa2-2c4f8ac7006e"
                                    type="bw.generalactivities.callprocess" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
                                    <bpws:targets>
                                    <bpws:target linkName="SearchMoviesToSortMovies"/>
                                    </bpws:targets>
                                    <bpws:sources>
                                    <bpws:source linkName="SortMoviesTogetOut"/>
                                    </bpws:sources>
                                    <tibex:inputBindings>
                                    <tibex:inputBinding
                                    expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; version=&quot;2.0&quot;>&lt;xsl:param name=&quot;SearchMovies&quot;/>&lt;xsl:template name=&quot;SortMovies-input&quot; match=&quot;/&quot;>&lt;xsl:copy-of select=&quot;$SearchMovies&quot;/>&lt;/xsl:template>&lt;/xsl:stylesheet>" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"/>
                                    </tibex:inputBindings>
                                    <tibex:CallProcess
                                    subProcessName="moviecatalogsearch.module.SortMovies" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions"/>
                                </tibex:extActivity>
                            </bpws:extensionActivity>
                        </bpws:flow>
                    </bpws:scope>
                </bpws:onMessage>
            </bpws:pick>
        </bpws:flow>
    </bpws:scope>
</bpws:process>
