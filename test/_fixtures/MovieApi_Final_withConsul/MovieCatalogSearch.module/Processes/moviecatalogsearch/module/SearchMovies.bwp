<?xml version="1.0" encoding="UTF-8"?>
<bpws:process exitOnStandardFault="no"
    name="moviecatalogsearch.module.SearchMovies"
    suppressJoinFailure="yes"
    targetNamespace="http://xmlns.example.com/20190722212639"
    xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
    xmlns:info="http://www.tibco.com/bw/process/info"
    xmlns:ns="http://www.tibco.com/pe/EngineTypes"
    xmlns:ns0="http://www.example.org/MovieCatalogMaster"
    xmlns:ns1="http://xmlns.example.com/20190722212639PLT"
    xmlns:ns2="http://tns.tibco.com/bw/palette/internal/activityerror+d737dfa9-a85c-4865-ac6c-3e5ce1556ef6"
    xmlns:ns3="http://xmlns.example.com/SearchMovies/parameters"
    xmlns:ns4="http://www.tibco.com/pe/WriteToLogActivitySchema"
    xmlns:sca="http://docs.oasis-open.org/ns/opencsa/sca/200912"
    xmlns:sca-bpel="http://docs.oasis-open.org/ns/opencsa/sca-bpel/200801"
    xmlns:tibex="http://www.tibco.com/bpel/2007/extensions"
    xmlns:tibprop="http://ns.tibco.com/bw/property" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <tibex:Types>
        <xs:schema attributeFormDefault="unqualified"
            elementFormDefault="qualified"
            targetNamespace="http://www.tibco.com/pe/EngineTypes"
            xmlns:tns="http://www.tibco.com/pe/EngineTypes" xmlns:xs="http://www.w3.org/2001/XMLSchema">
            <xs:complexType block="extension restriction"
                final="extension restriction" name="ErrorReport">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="StackTrace" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Msg" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="FullClass" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Class" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ProcessStack" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="MsgCode" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Data" type="tns:anydata"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="OptionalErrorReport">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="StackTrace" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Msg" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="FullClass" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Class" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="ProcessStack" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="MsgCode" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Data" type="tns:anydata"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="FaultDetail">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ActivityName" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Data" type="tns:anydata"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Msg" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="MsgCode" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ProcessStack" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="StackTrace" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="FullClass" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Class" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="ProcessContext">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="JobId" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ApplicationName" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="EngineName" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ProcessInstanceId" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="CustomJobId" type="xs:string"/>
                    <!--xs:element name="RestartedFromCheckpoint" form="unqualified" block="extension restriction substitution" type="xs:boolean"/-->
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" maxOccurs="unbounded"
                        minOccurs="0" name="TrackingInfo" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="anydata">
                <xs:sequence>
                    <xs:any namespace="##any" processContents="lax"/>
                </xs:sequence>
            </xs:complexType>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="OptionalErrorReport" type="tns:OptionalErrorReport"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="ErrorReport" type="tns:ErrorReport"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="FaultDetail" type="tns:FaultDetail"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="ProcessContext" type="tns:ProcessContext"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="CorrelationValue" type="xs:string"/>
        </xs:schema>
        <schema attributeFormDefault="unqualified"
            elementFormDefault="unqualified"
            targetNamespace="http://schemas.tibco.com/bw/pe/plugin/5.0/exceptions"
            version="" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.tibco.com/bw/pe/plugin/5.0/exceptions">
            <complexType name="ActivityExceptionType">
                <sequence>
                    <element name="msg" type="string"/>
                    <element minOccurs="0" name="msgCode" type="string"/>
                </sequence>
            </complexType>
            <element name="ActivityException" type="tns:ActivityExceptionType"/>
            <complexType name="ActivityTimedOutExceptionType">
                <complexContent>
                    <extension base="tns:ActivityExceptionType"/>
                </complexContent>
            </complexType>
            <element name="ActivityTimedOutException" type="tns:ActivityTimedOutExceptionType"/>
            <complexType name="DuplicateKeyExceptionType">
                <complexContent>
                    <extension base="tns:ActivityExceptionType">
                        <sequence>
                            <element name="duplicateKey" type="string"/>
                            <element minOccurs="0" name="previousJobID" type="string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>
            <element name="DuplicateKeyException" type="tns:DuplicateKeyExceptionType"/>
        </schema>
        <schema elementFormDefault="unqualified"
            targetNamespace="http://tns.tibco.com/bw/palette/internal/activityerror+d737dfa9-a85c-4865-ac6c-3e5ce1556ef6"
            xmlns="http://www.w3.org/2001/XMLSchema"
            xmlns:Q1="http://schemas.tibco.com/bw/pe/plugin/5.0/exceptions" xmlns:tns="http://tns.tibco.com/bw/palette/internal/activityerror+d737dfa9-a85c-4865-ac6c-3e5ce1556ef6">
            <import namespace="http://schemas.tibco.com/bw/pe/plugin/5.0/exceptions"/>
            <element name="ActivityErrorData" type="tns:ActivityErrorDataType"/>
            <complexType name="ActivityErrorDataType">
                <choice>
                    <element maxOccurs="1" minOccurs="0" ref="Q1:ActivityTimedOutException"/>
                </choice>
            </complexType>
        </schema>
        <xs:schema attributeFormDefault="unqualified"
            elementFormDefault="qualified"
            targetNamespace="http://www.tibco.com/pe/WriteToLogActivitySchema"
            xmlns:tns="http://www.tibco.com/pe/WriteToLogActivitySchema" xmlns:xs="http://www.w3.org/2001/XMLSchema">
            <xs:complexType name="LogParametersType">
                <xs:sequence>
                    <xs:element form="unqualified" minOccurs="0"
                        name="msgCode" type="xs:string"/>
                    <xs:element form="unqualified" minOccurs="0"
                        name="loggerName" type="xs:string"/>
                    <xs:element form="unqualified" minOccurs="0"
                        name="logLevel" type="xs:string"/>
                    <xs:element form="unqualified" name="message" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:element name="ActivityInput" type="tns:LogParametersType"/>
        </xs:schema>
        <wsdl:definitions
            targetNamespace="http://xmlns.example.com/20190722212639PLT"
            xmlns:extns="http://tns.tibco.com/bw/REST"
            xmlns:extns1="/T1563811039923Converted/JsonSchema"
            xmlns:extns2="http://xmlns.example.com/SearchMovies/parameters"
            xmlns:plnk="http://docs.oasis-open.org/wsbpel/2.0/plnktype"
            xmlns:tibex="http://www.tibco.com/bpel/2007/extensions"
            xmlns:tns="http://xmlns.example.com/20190722212639PLT"
            xmlns:vprop="http://docs.oasis-open.org/wsbpel/2.0/varprop"
            xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <plnk:partnerLinkType name="partnerLinkType">
                <plnk:role name="use" portType="tns:movies"/>
            </plnk:partnerLinkType>
            <wsdl:import namespace="http://tns.tibco.com/bw/REST"/>
            <wsdl:import namespace="/T1563811039923Converted/JsonSchema"/>
            <wsdl:import namespace="http://xmlns.example.com/SearchMovies/parameters"/>
            <wsdl:message name="getRequest">
                <wsdl:part element="extns:httpHeaders"
                    name="httpHeaders" tibex:source="bw.rest"/>
                <wsdl:part element="extns2:moviesGetParameters"
                    name="parameters" tibex:source="bw.rest"/>
            </wsdl:message>
            <wsdl:message name="getResponse">
                <wsdl:part element="extns1:OMDBSearchElement"
                    name="item" tibex:hasMultipleNamespaces="false"/>
            </wsdl:message>
            <wsdl:message name="get4XXFaultMessage">
                <wsdl:part element="extns:client4XXError" name="clientError"/>
            </wsdl:message>
            <wsdl:message name="get5XXFaultMessage">
                <wsdl:part element="extns:server5XXError" name="serverError"/>
            </wsdl:message>
            <wsdl:portType name="movies" tibex:bw.rest.apipath="/movies"
                tibex:bw.rest.host="localhost:8080"
                tibex:bw.rest.resource="Resources/swagger.json"
                tibex:bw.rest.resource.source="external" tibex:source="bw.rest.reference">
                <wsdl:documentation>summary documentation</wsdl:documentation>
                <wsdl:operation name="get">
                    <wsdl:input message="tns:getRequest" name="getInput"/>
                    <wsdl:output message="tns:getResponse" name="getOutput"/>
                    <wsdl:fault message="tns:get4XXFaultMessage" name="clientFault"/>
                    <wsdl:fault message="tns:get5XXFaultMessage" name="serverFault"/>
                </wsdl:operation>
            </wsdl:portType>
        </wsdl:definitions>
    </tibex:Types>
    <tibex:ProcessInfo callable="true" createdBy="awagle"
        createdOn="Mon Jul 22 21:26:39 IST 2019" description=""
        extraErrorVars="true" modifiers="public"
        productVersion="2.4.4 V34 2019-05-22" scalable="true"
        singleton="true" stateless="true" type="IT"/>
    <tibex:ProcessInterface context=""
        input="{http://xmlns.example.com/SearchMovies/parameters}moviesGetParameters" output="{http://www.example.org/MovieCatalogMaster}OMDBSearchElement"/>
    <tibex:ProcessTemplateConfigurations/>
    <notation:Diagram measurementUnit="Pixel" type="BWProcess"
        xmlns:bwnotation="http://tns.tibco.com/bw/runtime/BWNotation"
        xmlns:notation="http://www.eclipse.org/gmf/runtime/1.0.2/notation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <children type="2001">
            <children type="5001"/>
            <children type="3002">
                <styles xsi:type="notation:SortingStyle"/>
                <styles xsi:type="notation:FilteringStyle"/>
            </children>
            <children type="3003">
                <styles xsi:type="notation:SortingStyle"/>
                <styles xsi:type="notation:FilteringStyle"/>
            </children>
            <children type="3004">
                <children type="4018">
                    <children type="3018">
                        <children type="4020">
                            <children type="3020">
                                <children type="4005">
                                    <children type="3007">
                                    <children type="4002">
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.0"/>
                                    <layoutConstraint x="2"
                                    xsi:type="notation:Bounds" y="2"/>
                                    </children>
                                    <children type="4002">
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.1"/>
                                    <layoutConstraint x="366"
                                    xsi:type="notation:Bounds" y="2"/>
                                    </children>
                                    <children type="4002">
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.2"/>
                                    <layoutConstraint x="190"
                                    xsi:type="notation:Bounds" y="93"/>
                                    </children>
                                    <children type="4002 bw.generalactivities.log">
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.3"/>
                                    <layoutConstraint
                                    height="40" width="40"
                                    x="278"
                                    xsi:type="notation:Bounds" y="63"/>
                                    </children>
                                    <styles xsi:type="notation:DrawerStyle"/>
                                    <styles xsi:type="notation:SortingStyle"/>
                                    <styles xsi:type="notation:FilteringStyle"/>
                                    <element href="//0/@process/@activity/@activity"/>
                                    </children>
                                    <styles fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <styles xsi:type="bwnotation:ResizingStyle"/>
                                    <element href="//0/@process/@activity/@activity"/>
                                    <layoutConstraint height="384"
                                    width="515" xsi:type="notation:Bounds"/>
                                </children>
                                <styles xsi:type="notation:SortingStyle"/>
                                <styles xsi:type="notation:FilteringStyle"/>
                            </children>
                            <styles fontName=".SF NS Text" lineColor="0" xsi:type="notation:ShapeStyle"/>
                            <styles xsi:type="bwnotation:BackgroundStyle"/>
                            <styles xsi:type="bwnotation:ResizingStyle"/>
                            <element href="//0/@process/@activity"/>
                            <layoutConstraint height="365" width="444"
                                x="30" xsi:type="notation:Bounds" y="20"/>
                        </children>
                        <styles xsi:type="notation:SortingStyle"/>
                        <styles xsi:type="notation:FilteringStyle"/>
                        <element href="//0/@process/@activity"/>
                    </children>
                    <styles fontName=".SF NS Text" lineColor="0" xsi:type="notation:ShapeStyle"/>
                    <styles xsi:type="bwnotation:BackgroundStyle"/>
                    <styles xsi:type="bwnotation:ResizingStyle"/>
                    <element href="//0/@process/@activity"/>
                    <layoutConstraint height="409" width="519" xsi:type="notation:Bounds"/>
                </children>
                <styles xsi:type="notation:SortingStyle"/>
                <styles xsi:type="notation:FilteringStyle"/>
                <element href="//0/@process"/>
            </children>
            <styles fontName=".SF NS Text" lineColor="0" xsi:type="notation:ShapeStyle"/>
            <element href="//0/@process"/>
            <layoutConstraint height="460" width="860" xsi:type="notation:Bounds"/>
        </children>
        <styles xsi:type="notation:DiagramStyle"/>
        <element href="//0"/>
        <edges
            source="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0"
            target="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2" type="4006">
            <children type="6002">
                <layoutConstraint xsi:type="notation:Location" y="40"/>
            </children>
            <styles lineColor="0" xsi:type="notation:ConnectorStyle"/>
            <styles fontName=".SF NS Text" xsi:type="notation:FontStyle"/>
            <element href="//0/@process/@activity/@activity/@links/@children.0"/>
            <bendpoints points="[0, 0, 0, 0]$[0, 0, 0, 0]" xsi:type="notation:RelativeBendpoints"/>
            <targetAnchor id="(0.16260162601626016,0.4266666666666667)" xsi:type="notation:IdentityAnchor"/>
        </edges>
        <edges
            source="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2"
            target="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.3" type="4006">
            <children type="6002">
                <layoutConstraint xsi:type="notation:Location" y="40"/>
            </children>
            <styles lineColor="0" xsi:type="notation:ConnectorStyle"/>
            <styles fontName=".SF NS Text" xsi:type="notation:FontStyle"/>
            <element href="//0/@process/@activity/@activity/@links/@children.1"/>
            <bendpoints points="[25, -13, -151, 78]$[152, -78, -24, 13]" xsi:type="notation:RelativeBendpoints"/>
        </edges>
        <edges
            source="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.3"
            target="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.1" type="4006">
            <children type="6002">
                <layoutConstraint xsi:type="notation:Location" y="40"/>
            </children>
            <styles lineColor="0" xsi:type="notation:ConnectorStyle"/>
            <styles fontName=".SF NS Text" xsi:type="notation:FontStyle"/>
            <element href="//0/@process/@activity/@activity/@links/@children.2"/>
            <bendpoints points="[0, 0, 0, 0]$[0, 0, 0, 0]" xsi:type="notation:RelativeBendpoints"/>
        </edges>
    </notation:Diagram>
    <tibex:NamespaceRegistry enabled="true">
        <tibex:namespaceItem
            namespace="http://xmlns.example.com/20190722212639PLT" prefix="tns"/>
        <tibex:namespaceItem
            namespace="http://xmlns.example.com/SearchMovies/parameters" prefix="tns2"/>
        <tibex:namespaceItem namespace="http://tns.tibco.com/bw/REST" prefix="tns1"/>
        <tibex:namespaceItem
            namespace="http://www.tibco.com/pe/WriteToLogActivitySchema" prefix="tns3"/>
        <tibex:namespaceItem
            namespace="http://www.tibco.com/bw/xslt/custom-functions" prefix="tib"/>
        <tibex:namespaceItem
            namespace="http://www.example.org/MovieCatalogMaster" prefix="tns4"/>
    </tibex:NamespaceRegistry>
    <bpws:import importType="http://www.w3.org/2001/XMLSchema" namespace="http://www.example.org/MovieCatalogMaster"/>
    <bpws:import importType="http://www.w3.org/2001/XMLSchema" namespace="http://tns.tibco.com/bw/REST"/>
    <bpws:import importType="http://www.w3.org/2001/XMLSchema" namespace="/T1563811039923Converted/JsonSchema"/>
    <bpws:import importType="http://www.w3.org/2001/XMLSchema" namespace="http://xmlns.example.com/SearchMovies/parameters"/>
    <bpws:partnerLinks>
        <bpws:partnerLink name="movies"
            partnerLinkType="ns1:partnerLinkType" partnerRole="use"
            sca-bpel:ignore="true" sca-bpel:reference="movies" sca-bpel:wiredByImpl="false">
            <tibex:ReferenceBinding>
                <tibex:binding>
                    <bwbinding:BWBaseBinding
                        xmlns:bwbinding="http://tns.tibco.com/bw/model/core/bwbinding"
                        xmlns:rest="http://xsd.tns.tibco.com/bw/models/binding/rest"
                        xmlns:sca="http://www.osoa.org/xmlns/sca/1.0"
                        xmlns:scact="http://xsd.tns.tibco.com/amf/models/sca/componentType"
                        xmlns:scaext="http://xsd.tns.tibco.com/amf/models/sca/extensions" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                        <referenceBinding name="movies" xsi:type="scact:Reference">
                            <sca:interface.wsdl
                                interface="http://xmlns.example.com/20190722212639PLT#wsdl.interface(movies)" scaext:wsdlLocation=""/>
                            <scaext:binding basePath="/"
                                connector="moviecatalogsearch.module.HttpClientResource"
                                docBasePath="http://localhost:7777/"
                                docResourcePath="Movies"
                                implementation="Resources/swagger.json"
                                name="RestReference" path="/movies"
                                structuredData="true"
                                technologyVersion="2.0" xsi:type="rest:RestReferenceBinding">
                                <operation httpMethod="GET"
                                    operationName="get"
                                    requestEntityProcessing="chunked" responseStyle="element">
                                    <parameters>
                                    <parameterMapping
                                    dataType="string"
                                    parameterName="searchString"
                                    parameterType="Query" required="true"/>
                                    </parameters>
                                    <clientFormat>json</clientFormat>
                                </operation>
                                <parameters/>
                                <advancedConfig blockingQueueSize="2147483647"/>
                            </scaext:binding>
                        </referenceBinding>
                    </bwbinding:BWBaseBinding>
                </tibex:binding>
            </tibex:ReferenceBinding>
        </bpws:partnerLink>
    </bpws:partnerLinks>
    <bpws:variables>
        <bpws:variable element="ns:ProcessContext"
            name="_processContext" sca-bpel:internal="true"/>
        <bpws:variable element="ns0:OMDBSearchElement" name="End-input"
            sca-bpel:internal="true" tibex:parameter="out"/>
        <bpws:variable messageType="ns1:getRequest" name="get-input" sca-bpel:internal="true"/>
        <bpws:variable messageType="ns1:getResponse" name="get" sca-bpel:internal="true"/>
        <bpws:variable element="ns2:ActivityErrorData" name="_error_get" sca-bpel:internal="true"/>
        <bpws:variable element="ns:ErrorReport" name="_error" sca-bpel:internal="true"/>
        <bpws:variable element="ns3:moviesGetParameters" name="Start"
            sca-bpel:internal="true" tibex:parameter="in"/>
        <bpws:variable element="ns4:ActivityInput" name="Log-input" sca-bpel:internal="true"/>
    </bpws:variables>
    <bpws:extensions>
        <bpws:extension mustUnderstand="no" namespace="http://www.eclipse.org/gmf/runtime/1.0.2/notation"/>
        <bpws:extension mustUnderstand="no" namespace="http://www.tibco.com/bw/process/info"/>
        <bpws:extension mustUnderstand="no" namespace="http://docs.oasis-open.org/ns/opencsa/sca-bpel/200801"/>
        <bpws:extension mustUnderstand="no" namespace="http://docs.oasis-open.org/ns/opencsa/sca/200912"/>
        <bpws:extension mustUnderstand="no" namespace="http://ns.tibco.com/bw/property"/>
        <bpws:extension mustUnderstand="no" namespace="http://www.tibco.com/bpel/2007/extensions"/>
    </bpws:extensions>
    <bpws:scope name="scope">
        <bpws:flow name="flow">
            <bpws:links>
                <bpws:link name="StartToget" tibex:linkType="SUCCESS"/>
                <bpws:link name="getToEnd" tibex:linkType="SUCCESS"/>
                <bpws:link name="LogToEnd" tibex:linkType="SUCCESS"/>
            </bpws:links>
            <bpws:extensionActivity>
                <tibex:receiveEvent createInstance="yes"
                    eventTimeout="0" name="Start"
                    tibex:xpdlId="3929021d-0c9a-4203-8416-3d4eef2734dd"
                    variable="Start" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
                    <bpws:sources>
                        <bpws:source linkName="StartToget"/>
                    </bpws:sources>
                    <tibex:eventSource>
                        <tibex:StartEvent xmlns:tibex="http://www.tibco.com/bpel/2007/extensions"/>
                    </tibex:eventSource>
                </tibex:receiveEvent>
            </bpws:extensionActivity>
            <bpws:extensionActivity>
                <tibex:activityExtension
                    expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns4=&quot;http://www.example.org/MovieCatalogMaster&quot; xmlns:tns=&quot;/T1563811039923Converted/JsonSchema&quot; version=&quot;2.0&quot;>&lt;xsl:param name=&quot;get&quot;/>&lt;xsl:template name=&quot;End-input&quot; match=&quot;/&quot;>&lt;tns4:OMDBSearchElement>&lt;xsl:for-each select=&quot;$get/item/tns:OMDBSearchElement/tns:Search&quot;>&lt;tns4:Search>&lt;xsl:if test=&quot;tns:Title&quot;>&lt;tns4:Title>&lt;xsl:value-of select=&quot;tns:Title&quot;/>&lt;/tns4:Title>&lt;/xsl:if>&lt;xsl:if test=&quot;tns:Year&quot;>&lt;tns4:Year>&lt;xsl:value-of select=&quot;tns:Year&quot;/>&lt;/tns4:Year>&lt;/xsl:if>&lt;xsl:if test=&quot;tns:imdbID&quot;>&lt;tns4:imdbID>&lt;xsl:value-of select=&quot;tns:imdbID&quot;/>&lt;/tns4:imdbID>&lt;/xsl:if>&lt;xsl:if test=&quot;tns:Type&quot;>&lt;tns4:Type>&lt;xsl:value-of select=&quot;tns:Type&quot;/>&lt;/tns4:Type>&lt;/xsl:if>&lt;xsl:if test=&quot;tns:Poster&quot;>&lt;tns4:Poster>&lt;xsl:value-of select=&quot;tns:Poster&quot;/>&lt;/tns4:Poster>&lt;/xsl:if>&lt;/tns4:Search>&lt;/xsl:for-each>&lt;xsl:if test=&quot;$get/item/tns:OMDBSearchElement/tns:totalResults&quot;>&lt;tns4:totalResults>&lt;xsl:value-of select=&quot;$get/item/tns:OMDBSearchElement/tns:totalResults&quot;/>&lt;/tns4:totalResults>&lt;/xsl:if>&lt;xsl:if test=&quot;$get/item/tns:OMDBSearchElement/tns:Response&quot;>&lt;tns4:Response>&lt;xsl:value-of select=&quot;$get/item/tns:OMDBSearchElement/tns:Response&quot;/>&lt;/tns4:Response>&lt;/xsl:if>&lt;/tns4:OMDBSearchElement>&lt;/xsl:template>&lt;/xsl:stylesheet>"
                    expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"
                    inputVariable="End-input" name="End"
                    tibex:xpdlId="ab1c439b-2f9e-4f31-8315-f3331d23cac8" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
                    <bpws:targets>
                        <bpws:target linkName="LogToEnd"/>
                    </bpws:targets>
                    <tibex:inputBindings>
                        <tibex:inputBinding
                            expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns4=&quot;http://www.example.org/MovieCatalogMaster&quot; xmlns:tns=&quot;/T1563811039923Converted/JsonSchema&quot; version=&quot;2.0&quot;>&#xa;    &lt;xsl:param name=&quot;get.item&quot;/>&#xa;    &lt;xsl:template name=&quot;End-input&quot; match=&quot;/&quot;>&#xa;        &lt;tns4:OMDBSearchElement>&#xa;            &lt;xsl:for-each select=&quot;$get.item/tns:Search&quot;>&#xa;                &lt;tns4:Search>&#xa;                    &lt;xsl:if test=&quot;tns:Title&quot;>&#xa;                        &lt;tns4:Title>&#xa;                            &lt;xsl:value-of select=&quot;tns:Title&quot;/>&#xa;                        &lt;/tns4:Title>&#xa;                    &lt;/xsl:if>&#xa;                    &lt;xsl:if test=&quot;tns:Year&quot;>&#xa;                        &lt;tns4:Year>&#xa;                            &lt;xsl:value-of select=&quot;tns:Year&quot;/>&#xa;                        &lt;/tns4:Year>&#xa;                    &lt;/xsl:if>&#xa;                    &lt;xsl:if test=&quot;tns:imdbID&quot;>&#xa;                        &lt;tns4:imdbID>&#xa;                            &lt;xsl:value-of select=&quot;tns:imdbID&quot;/>&#xa;                        &lt;/tns4:imdbID>&#xa;                    &lt;/xsl:if>&#xa;                    &lt;xsl:if test=&quot;tns:Type&quot;>&#xa;                        &lt;tns4:Type>&#xa;                            &lt;xsl:value-of select=&quot;tns:Type&quot;/>&#xa;                        &lt;/tns4:Type>&#xa;                    &lt;/xsl:if>&#xa;                    &lt;xsl:if test=&quot;tns:Poster&quot;>&#xa;                        &lt;tns4:Poster>&#xa;                            &lt;xsl:value-of select=&quot;tns:Poster&quot;/>&#xa;                        &lt;/tns4:Poster>&#xa;                    &lt;/xsl:if>&#xa;                &lt;/tns4:Search>&#xa;            &lt;/xsl:for-each>&#xa;            &lt;xsl:if test=&quot;$get.item/tns:totalResults&quot;>&#xa;                &lt;tns4:totalResults>&#xa;                    &lt;xsl:value-of select=&quot;$get.item/tns:totalResults&quot;/>&#xa;                &lt;/tns4:totalResults>&#xa;            &lt;/xsl:if>&#xa;            &lt;xsl:if test=&quot;$get.item/tns:Response&quot;>&#xa;                &lt;tns4:Response>&#xa;                    &lt;xsl:value-of select=&quot;$get.item/tns:Response&quot;/>&#xa;                &lt;/tns4:Response>&#xa;            &lt;/xsl:if>&#xa;        &lt;/tns4:OMDBSearchElement>&#xa;    &lt;/xsl:template>&#xa;&lt;/xsl:stylesheet>" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"/>
                    </tibex:inputBindings>
                    <tibex:config>
                        <bwext:BWActivity
                            activityTypeID="bw.internal.end"
                            xmlns:activityconfig="http://tns.tibco.com/bw/model/activityconfig"
                            xmlns:bwext="http://tns.tibco.com/bw/model/core/bwext"
                            xmlns:internalactivities="http://ns.tibco.com/bw/core/internalactivity" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                            <activityConfig>
                                <properties name="config" xsi:type="activityconfig:EMFProperty">
                                    <type href="http://ns.tibco.com/bw/core/internalactivity#//End"/>
                                    <value xsi:type="internalactivities:End"/>
                                </properties>
                            </activityConfig>
                        </bwext:BWActivity>
                    </tibex:config>
                </tibex:activityExtension>
            </bpws:extensionActivity>
            <bpws:invoke inputVariable="get-input" name="get"
                operation="get" outputVariable="get"
                partnerLink="movies" portType="ns1:movies" tibex:xpdlId="d737dfa9-a85c-4865-ac6c-3e5ce1556ef6">
                <tibex:inputBinding expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0">&lt;?xml version="1.0" encoding="UTF-8"?&gt;
&lt;xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:tns="http://xmlns.example.com/20190722212639PLT" xmlns:tns1="http://tns.tibco.com/bw/REST" xmlns:tns2="http://xmlns.example.com/SearchMovies/parameters" version="2.0"&gt;&lt;xsl:param name="Start"/&gt;&lt;xsl:template name="get-input" match="/"&gt;&lt;tns:getRequest&gt;&lt;httpHeaders&gt;&lt;tns1:httpHeaders/&gt;&lt;/httpHeaders&gt;&lt;parameters&gt;&lt;tns2:moviesGetParameters&gt;&lt;tns2:searchString&gt;&lt;xsl:value-of select="$Start/tns2:searchString"/&gt;&lt;/tns2:searchString&gt;&lt;/tns2:moviesGetParameters&gt;&lt;/parameters&gt;&lt;/tns:getRequest&gt;&lt;/xsl:template&gt;&lt;/xsl:stylesheet&gt;</tibex:inputBinding>
                <tibex:inputBindings>
                    <tibex:partBinding
                        expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns=&quot;http://xmlns.example.com/20190722212639PLT&quot; xmlns:tns1=&quot;http://tns.tibco.com/bw/REST&quot; xmlns:tns2=&quot;http://xmlns.example.com/SearchMovies/parameters&quot; version=&quot;2.0&quot;>&#xa;    &lt;xsl:template name=&quot;get-input&quot; match=&quot;/&quot;>&#xa;        &lt;tns1:httpHeaders/>&#xa;    &lt;/xsl:template>&#xa;&lt;/xsl:stylesheet>" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"/>
                    <tibex:partBinding
                        expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns=&quot;http://xmlns.example.com/20190722212639PLT&quot; xmlns:tns1=&quot;http://tns.tibco.com/bw/REST&quot; xmlns:tns2=&quot;http://xmlns.example.com/SearchMovies/parameters&quot; version=&quot;2.0&quot;>&#xa;    &lt;xsl:param name=&quot;Start&quot;/>&#xa;    &lt;xsl:template name=&quot;get-input&quot; match=&quot;/&quot;>&#xa;        &lt;tns2:moviesGetParameters>&#xa;            &lt;tns2:searchString>&#xa;                &lt;xsl:value-of select=&quot;$Start/tns2:searchString&quot;/>&#xa;            &lt;/tns2:searchString>&#xa;        &lt;/tns2:moviesGetParameters>&#xa;    &lt;/xsl:template>&#xa;&lt;/xsl:stylesheet>" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"/>
                </tibex:inputBindings>
                <bpws:targets>
                    <bpws:target linkName="StartToget"/>
                </bpws:targets>
                <bpws:sources>
                    <bpws:source linkName="getToEnd"/>
                </bpws:sources>
            </bpws:invoke>
            <bpws:extensionActivity>
                <tibex:activityExtension
                    expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns3=&quot;http://www.tibco.com/pe/WriteToLogActivitySchema&quot; xmlns:tns5=&quot;/T1563811039923Converted/JsonSchema&quot; xmlns:tib=&quot;http://www.tibco.com/bw/xslt/custom-functions&quot; version=&quot;2.0&quot;>&#xa;    &lt;xsl:param name=&quot;get&quot;/>&#xa;    &lt;xsl:template name=&quot;Log-input&quot; match=&quot;/&quot;>&#xa;        &lt;tns3:ActivityInput>&#xa;            &lt;message>&#xa;                &lt;xsl:value-of select=&quot;concat(&amp;quot;Search Service Response&amp;quot;, tib:render-xml($get/item/tns5:OMDBSearchElement))&quot;/>&#xa;            &lt;/message>&#xa;        &lt;/tns3:ActivityInput>&#xa;    &lt;/xsl:template>&#xa;&lt;/xsl:stylesheet>"
                    expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"
                    inputVariable="Log-input" name="Log"
                    tibex:xpdlId="ef69755a-b9bf-4efe-9c1b-dcce727a1469" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
                    <bpws:targets>
                        <bpws:target linkName="getToEnd"/>
                    </bpws:targets>
                    <bpws:sources>
                        <bpws:source linkName="LogToEnd"/>
                    </bpws:sources>
                    <tibex:inputBindings>
                        <tibex:inputBinding
                            expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns3=&quot;http://www.tibco.com/pe/WriteToLogActivitySchema&quot; xmlns:tns5=&quot;/T1563811039923Converted/JsonSchema&quot; xmlns:tib=&quot;http://www.tibco.com/bw/xslt/custom-functions&quot; version=&quot;2.0&quot;>&#xa;    &lt;xsl:param name=&quot;get.item&quot;/>&#xa;    &lt;xsl:template name=&quot;Log-input&quot; match=&quot;/&quot;>&#xa;        &lt;tns3:ActivityInput>&#xa;            &lt;message>&#xa;                &lt;xsl:value-of select=&quot;concat(&amp;quot;Search Service Response&amp;quot;, tib:render-xml($get.item))&quot;/>&#xa;            &lt;/message>&#xa;        &lt;/tns3:ActivityInput>&#xa;    &lt;/xsl:template>&#xa;&lt;/xsl:stylesheet>" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"/>
                    </tibex:inputBindings>
                    <tibex:config>
                        <bwext:BWActivity
                            activityTypeID="bw.generalactivities.log"
                            version="6.0.0.001"
                            xmlns:activityconfig="http://tns.tibco.com/bw/model/activityconfig"
                            xmlns:bwext="http://tns.tibco.com/bw/model/core/bwext"
                            xmlns:generalactivities="http://ns.tibco.com/bw/palette/generalactivities" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                            <activityConfig>
                                <properties name="config" xsi:type="activityconfig:EMFProperty">
                                    <type href="http://ns.tibco.com/bw/palette/generalactivities#//Log"/>
                                    <value role="Info"
                                    suppressJobInfo="true" xsi:type="generalactivities:Log"/>
                                </properties>
                            </activityConfig>
                        </bwext:BWActivity>
                    </tibex:config>
                </tibex:activityExtension>
            </bpws:extensionActivity>
        </bpws:flow>
    </bpws:scope>
</bpws:process>
