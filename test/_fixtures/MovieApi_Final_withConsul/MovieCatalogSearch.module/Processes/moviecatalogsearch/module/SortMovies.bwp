<?xml version="1.0" encoding="UTF-8"?>
<bpws:process exitOnStandardFault="no"
    name="moviecatalogsearch.module.SortMovies"
    suppressJoinFailure="yes"
    targetNamespace="http://xmlns.example.com/20190722213305"
    xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
    xmlns:info="http://www.tibco.com/bw/process/info"
    xmlns:ns="http://www.tibco.com/pe/EngineTypes"
    xmlns:ns0="http://www.example.org/MovieCatalogMaster"
    xmlns:ns1="http://xmlns.example.com/20190722213305PLT"
    xmlns:ns2="http://tns.tibco.com/bw/palette/internal/activityerror+fda3aef3-1a31-45de-bc3f-472e507cb81f"
    xmlns:ns3="http://tns.tibco.com/bw/palette/internal/accumulateoutput+de089a14-1d74-4f89-a1d4-82d52a2c2c4f"
    xmlns:ns4="http://tns.tibco.com/bw/palette/internal/activityerror+83cc635f-6d61-4b30-8898-711e065cb01e"
    xmlns:ns5="http://www.tibco.com/pe/WriteToLogActivitySchema"
    xmlns:ns6="http://tns.tibco.com/bw/palette/internal/accumulateoutput+4721fe34-3f61-4d32-847b-d3abbd59ed04"
    xmlns:sca="http://docs.oasis-open.org/ns/opencsa/sca/200912"
    xmlns:sca-bpel="http://docs.oasis-open.org/ns/opencsa/sca-bpel/200801"
    xmlns:tibex="http://www.tibco.com/bpel/2007/extensions"
    xmlns:tibprop="http://ns.tibco.com/bw/property" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <tibex:Types>
        <xs:schema attributeFormDefault="unqualified"
            elementFormDefault="qualified"
            targetNamespace="http://www.tibco.com/pe/EngineTypes"
            xmlns:tns="http://www.tibco.com/pe/EngineTypes" xmlns:xs="http://www.w3.org/2001/XMLSchema">
            <xs:complexType block="extension restriction"
                final="extension restriction" name="ErrorReport">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="StackTrace" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Msg" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="FullClass" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Class" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ProcessStack" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="MsgCode" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Data" type="tns:anydata"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="OptionalErrorReport">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="StackTrace" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Msg" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="FullClass" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Class" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="ProcessStack" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="MsgCode" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Data" type="tns:anydata"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="FaultDetail">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ActivityName" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Data" type="tns:anydata"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Msg" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="MsgCode" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ProcessStack" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="StackTrace" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="FullClass" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Class" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="ProcessContext">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="JobId" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ApplicationName" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="EngineName" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ProcessInstanceId" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="CustomJobId" type="xs:string"/>
                    <!--xs:element name="RestartedFromCheckpoint" form="unqualified" block="extension restriction substitution" type="xs:boolean"/-->
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" maxOccurs="unbounded"
                        minOccurs="0" name="TrackingInfo" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="anydata">
                <xs:sequence>
                    <xs:any namespace="##any" processContents="lax"/>
                </xs:sequence>
            </xs:complexType>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="OptionalErrorReport" type="tns:OptionalErrorReport"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="ErrorReport" type="tns:ErrorReport"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="FaultDetail" type="tns:FaultDetail"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="ProcessContext" type="tns:ProcessContext"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="CorrelationValue" type="xs:string"/>
        </xs:schema>
        <schema attributeFormDefault="unqualified"
            elementFormDefault="unqualified"
            targetNamespace="http://schemas.tibco.com/bw/pe/plugin/5.0/exceptions"
            version="" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.tibco.com/bw/pe/plugin/5.0/exceptions">
            <complexType name="ActivityExceptionType">
                <sequence>
                    <element name="msg" type="string"/>
                    <element minOccurs="0" name="msgCode" type="string"/>
                </sequence>
            </complexType>
            <element name="ActivityException" type="tns:ActivityExceptionType"/>
            <complexType name="ActivityTimedOutExceptionType">
                <complexContent>
                    <extension base="tns:ActivityExceptionType"/>
                </complexContent>
            </complexType>
            <element name="ActivityTimedOutException" type="tns:ActivityTimedOutExceptionType"/>
            <complexType name="DuplicateKeyExceptionType">
                <complexContent>
                    <extension base="tns:ActivityExceptionType">
                        <sequence>
                            <element name="duplicateKey" type="string"/>
                            <element minOccurs="0" name="previousJobID" type="string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>
            <element name="DuplicateKeyException" type="tns:DuplicateKeyExceptionType"/>
        </schema>
        <schema attributeFormDefault="unqualified"
            elementFormDefault="qualified"
            targetNamespace="http://www.example.org/MovieCatalogMaster"
            xmlns="http://www.w3.org/2001/XMLSchema"
            xmlns:source="bw.group.iterate"
            xmlns:tns="http://www.example.org/MovieCatalogMaster"
            xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <element name="Iterate_Current_Element_Search">
                <complexType>
                    <sequence maxOccurs="1" minOccurs="1">
                        <element maxOccurs="1" minOccurs="0"
                            name="Title" nillable="false" type="string"/>
                        <element maxOccurs="1" minOccurs="0" name="Year"
                            nillable="false" type="string"/>
                        <element maxOccurs="1" minOccurs="0"
                            name="imdbID" nillable="false" type="string"/>
                        <element maxOccurs="1" minOccurs="0" name="Type"
                            nillable="false" type="string"/>
                        <element maxOccurs="1" minOccurs="0"
                            name="Poster" nillable="false" type="string"/>
                    </sequence>
                </complexType>
            </element>
        </schema>
        <schema elementFormDefault="unqualified"
            targetNamespace="http://tns.tibco.com/bw/palette/internal/activityerror+83cc635f-6d61-4b30-8898-711e065cb01e"
            xmlns="http://www.w3.org/2001/XMLSchema"
            xmlns:Q1="http://schemas.tibco.com/bw/pe/plugin/5.0/exceptions"
            xmlns:Q2="http://tns.tibco.com/bw/REST" xmlns:tns="http://tns.tibco.com/bw/palette/internal/activityerror+83cc635f-6d61-4b30-8898-711e065cb01e">
            <import namespace="http://tns.tibco.com/bw/REST"/>
            <import namespace="http://schemas.tibco.com/bw/pe/plugin/5.0/exceptions"/>
            <element name="ActivityErrorData" type="tns:ActivityErrorDataType"/>
            <complexType name="ActivityErrorDataType">
                <choice>
                    <element maxOccurs="1" minOccurs="0" ref="Q1:ActivityTimedOutException"/>
                    <element maxOccurs="1" minOccurs="1" ref="Q2:client4XXError"/>
                    <element maxOccurs="1" minOccurs="1" ref="Q2:server5XXError"/>
                </choice>
            </complexType>
        </schema>
        <xs:schema attributeFormDefault="unqualified"
            elementFormDefault="qualified"
            targetNamespace="http://www.tibco.com/pe/WriteToLogActivitySchema"
            xmlns:tns="http://www.tibco.com/pe/WriteToLogActivitySchema" xmlns:xs="http://www.w3.org/2001/XMLSchema">
            <xs:complexType name="LogParametersType">
                <xs:sequence>
                    <xs:element form="unqualified" minOccurs="0"
                        name="msgCode" type="xs:string"/>
                    <xs:element form="unqualified" minOccurs="0"
                        name="loggerName" type="xs:string"/>
                    <xs:element form="unqualified" minOccurs="0"
                        name="logLevel" type="xs:string"/>
                    <xs:element form="unqualified" name="message" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:element name="ActivityInput" type="tns:LogParametersType"/>
        </xs:schema>
        <xs:schema elementFormDefault="qualified"
            targetNamespace="http://tns.tibco.com/bw/palette/internal/accumulateoutput+4721fe34-3f61-4d32-847b-d3abbd59ed04"
            xmlns:Q1="http://www.example.org/MovieCatalogMaster"
            xmlns:tns="http://tns.tibco.com/bw/palette/internal/accumulateoutput+4721fe34-3f61-4d32-847b-d3abbd59ed04" xmlns:xs="http://www.w3.org/2001/XMLSchema">
            <xs:import namespace="http://www.example.org/MovieCatalogMaster"/>
            <xs:element name="AccumulatedOutput" type="tns:AccumulatedOutputType"/>
            <xs:complexType name="AccumulatedOutputType">
                <xs:sequence maxOccurs="1" minOccurs="0">
                    <xs:element maxOccurs="unbounded" minOccurs="0" ref="Q1:MovieDetails"/>
                </xs:sequence>
            </xs:complexType>
        </xs:schema>
        <wsdl:definitions
            targetNamespace="http://xmlns.example.com/20190722213305PLT"
            xmlns:extns="http://tns.tibco.com/bw/REST"
            xmlns:extns1="http://www.example.org/MovieCatalogMaster"
            xmlns:extns2="http://xmlns.example.com/Default/parameters"
            xmlns:plnk="http://docs.oasis-open.org/wsbpel/2.0/plnktype"
            xmlns:tibex="http://www.tibco.com/bpel/2007/extensions"
            xmlns:tns="http://xmlns.example.com/20190722213305PLT"
            xmlns:vprop="http://docs.oasis-open.org/wsbpel/2.0/varprop"
            xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <plnk:partnerLinkType name="partnerLinkType">
                <plnk:role name="use" portType="tns:Www-omdbapi-com"/>
            </plnk:partnerLinkType>
            <wsdl:import namespace="http://tns.tibco.com/bw/REST"/>
            <wsdl:import namespace="http://www.example.org/MovieCatalogMaster"/>
            <wsdl:import namespace="http://xmlns.example.com/Default/parameters"/>
            <wsdl:message name="getRequest">
                <wsdl:part element="extns:httpHeaders"
                    name="httpHeaders" tibex:source="bw.rest"/>
                <wsdl:part element="extns2:Www-omdbapi-comGetParameters"
                    name="parameters" tibex:source="bw.rest"/>
            </wsdl:message>
            <wsdl:message name="getResponse">
                <wsdl:part element="extns1:MovieDetails" name="item" tibex:hasMultipleNamespaces="false"/>
            </wsdl:message>
            <wsdl:message name="get4XXFaultMessage">
                <wsdl:part element="extns:client4XXError" name="clientError"/>
            </wsdl:message>
            <wsdl:message name="get5XXFaultMessage">
                <wsdl:part element="extns:server5XXError" name="serverError"/>
            </wsdl:message>
            <wsdl:portType name="Www-omdbapi-com"
                tibex:bw.rest.apipath="/" tibex:bw.rest.basepath="/"
                tibex:bw.rest.host="www.omdbapi.com"
                tibex:bw.rest.resource.source="generated" tibex:source="bw.rest.reference">
                <wsdl:documentation>summary documentation</wsdl:documentation>
                <wsdl:operation name="get">
                    <wsdl:input message="tns:getRequest" name="getInput"/>
                    <wsdl:output message="tns:getResponse" name="getOutput"/>
                    <wsdl:fault message="tns:get4XXFaultMessage" name="clientFault"/>
                    <wsdl:fault message="tns:get5XXFaultMessage" name="serverFault"/>
                </wsdl:operation>
            </wsdl:portType>
        </wsdl:definitions>
    </tibex:Types>
    <tibex:ProcessInfo callable="true" createdBy="awagle"
        createdOn="Mon Jul 22 21:33:05 IST 2019" description=""
        extraErrorVars="true" modifiers="public"
        productVersion="2.4.4 V34 2019-05-22" scalable="true"
        singleton="true" stateless="true" type="IT"/>
    <tibex:ProcessInterface context=""
        input="{http://www.example.org/MovieCatalogMaster}OMDBSearchElement" output="{http://www.example.org/MovieCatalogMaster}Movie"/>
    <tibex:ProcessTemplateConfigurations/>
    <notation:Diagram measurementUnit="Pixel" type="BWProcess"
        xmlns:bwnotation="http://tns.tibco.com/bw/runtime/BWNotation"
        xmlns:notation="http://www.eclipse.org/gmf/runtime/1.0.2/notation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <children type="2001">
            <children type="5001"/>
            <children type="3002">
                <styles xsi:type="notation:SortingStyle"/>
                <styles xsi:type="notation:FilteringStyle"/>
            </children>
            <children type="3003">
                <styles xsi:type="notation:SortingStyle"/>
                <styles xsi:type="notation:FilteringStyle"/>
            </children>
            <children type="3004">
                <children type="4018">
                    <children type="3018">
                        <children type="4020">
                            <children type="3020">
                                <children type="4005">
                                    <children type="3007">
                                    <children type="4002">
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.0"/>
                                    <layoutConstraint x="-53"
                                    xsi:type="notation:Bounds" y="59"/>
                                    </children>
                                    <children type="4002">
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.1"/>
                                    <layoutConstraint x="616"
                                    xsi:type="notation:Bounds" y="98"/>
                                    </children>
                                    <children type="4018">
                                    <children type="3018">
                                    <children type="4020">
                                    <children type="3020">
                                    <children type="4005">
                                    <children type="3007">

                                    <children type="4002 bw.generalactivities.mapper">

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>

                                    <element href="//0/@process/@activity/@activity/@activities.2/@activity/@activities.1/@activity/@activity/@activities.2"/>

                                    <layoutConstraint
                                    height="40"
                                    width="40"
                                    x="239"
                                    xsi:type="notation:Bounds" y="31"/>
                                    </children>

                                    <children type="4002">

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>

                                    <element href="//0/@process/@activity/@activity/@activities.2/@activity/@activities.1/@activity/@activity/@activities.3"/>

                                    <layoutConstraint
                                    x="40"
                                    xsi:type="notation:Bounds" y="63"/>
                                    </children>

                                    <children type="4002 bw.generalactivities.log">

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>

                                    <element href="//0/@process/@activity/@activity/@activities.2/@activity/@activities.1/@activity/@activity/@activities.4"/>

                                    <layoutConstraint
                                    height="40"
                                    width="40"
                                    x="129"
                                    xsi:type="notation:Bounds" y="-47"/>
                                    </children>

                                    <children type="4002 bw.generalactivities.log">

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <children type="4017">

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>

                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>

                                    <element href="//0/@process/@activity/@activity/@activities.2/@activity/@activities.1/@activity/@activity/@activities.5"/>

                                    <layoutConstraint
                                    height="40"
                                    width="40"
                                    x="-29"
                                    xsi:type="notation:Bounds" y="-17"/>
                                    </children>
                                    <styles xsi:type="notation:DrawerStyle"/>
                                    <styles xsi:type="notation:SortingStyle"/>
                                    <styles xsi:type="notation:FilteringStyle"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <styles xsi:type="bwnotation:ResizingStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.2/@activity/@activities.1/@activity/@activity"/>

                                    <layoutConstraint
                                    height="245"
                                    width="489" xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles xsi:type="notation:SortingStyle"/>
                                    <styles xsi:type="notation:FilteringStyle"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles xsi:type="bwnotation:BackgroundStyle"/>
                                    <styles xsi:type="bwnotation:ResizingStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.2"/>
                                    <layoutConstraint
                                    height="245"
                                    width="492"
                                    x="15"
                                    xsi:type="notation:Bounds" y="20"/>
                                    </children>
                                    <children type="4002">
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.2/@activity/@activities.1/@activity/@activity/@activities.0"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4002">
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>

                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.2/@activity/@activities.1/@activity/@activity/@activities.1"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles xsi:type="notation:SortingStyle"/>
                                    <styles xsi:type="notation:FilteringStyle"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles xsi:type="bwnotation:BackgroundStyle"/>
                                    <styles xsi:type="bwnotation:ResizingStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.2"/>
                                    <layoutConstraint
                                    height="289" width="532"
                                    x="50"
                                    xsi:type="notation:Bounds" y="32"/>
                                    </children>
                                    <children type="4002 bw.generalactivities.log">
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.3"/>
                                    <layoutConstraint
                                    height="40" width="40"
                                    x="562"
                                    xsi:type="notation:Bounds" y="98"/>
                                    </children>
                                    <styles xsi:type="notation:DrawerStyle"/>
                                    <styles xsi:type="notation:SortingStyle"/>
                                    <styles xsi:type="notation:FilteringStyle"/>
                                    <element href="//0/@process/@activity/@activity"/>
                                    </children>
                                    <styles fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <styles xsi:type="bwnotation:ResizingStyle"/>
                                    <element href="//0/@process/@activity/@activity"/>
                                    <layoutConstraint height="384"
                                    width="796" xsi:type="notation:Bounds"/>
                                </children>
                                <styles xsi:type="notation:SortingStyle"/>
                                <styles xsi:type="notation:FilteringStyle"/>
                            </children>
                            <styles fontName=".SF NS Text" lineColor="0" xsi:type="notation:ShapeStyle"/>
                            <styles xsi:type="bwnotation:BackgroundStyle"/>
                            <styles xsi:type="bwnotation:ResizingStyle"/>
                            <element href="//0/@process/@activity"/>
                            <layoutConstraint height="384" width="799" xsi:type="notation:Bounds"/>
                        </children>
                        <styles xsi:type="notation:SortingStyle"/>
                        <styles xsi:type="notation:FilteringStyle"/>
                        <element href="//0/@process/@activity"/>
                    </children>
                    <styles fontName=".SF NS Text" lineColor="0" xsi:type="notation:ShapeStyle"/>
                    <styles xsi:type="bwnotation:BackgroundStyle"/>
                    <styles xsi:type="bwnotation:ResizingStyle"/>
                    <element href="//0/@process/@activity"/>
                    <layoutConstraint height="408" width="799" x="1"
                        xsi:type="notation:Bounds" y="1"/>
                </children>
                <styles xsi:type="notation:SortingStyle"/>
                <styles xsi:type="notation:FilteringStyle"/>
                <element href="//0/@process"/>
            </children>
            <styles fontName=".SF NS Text" lineColor="0" xsi:type="notation:ShapeStyle"/>
            <element href="//0/@process"/>
            <layoutConstraint height="460" width="1141" xsi:type="notation:Bounds"/>
        </children>
        <styles xsi:type="notation:DiagramStyle"/>
        <element href="//0"/>
        <edges
            source="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0"
            target="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2" type="4006">
            <children type="6002">
                <layoutConstraint xsi:type="notation:Location" y="40"/>
            </children>
            <styles lineColor="0" xsi:type="notation:ConnectorStyle"/>
            <styles fontName=".SF NS Text" xsi:type="notation:FontStyle"/>
            <element href="//0/@process/@activity/@activity/@links/@children.0"/>
            <bendpoints points="[0, 0, 0, 0]$[0, 0, 0, 0]" xsi:type="notation:RelativeBendpoints"/>
        </edges>
        <edges
            source="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2"
            target="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.3" type="4006">
            <children type="6002">
                <layoutConstraint xsi:type="notation:Location" y="40"/>
            </children>
            <styles lineColor="0" xsi:type="notation:ConnectorStyle"/>
            <styles fontName=".SF NS Text" xsi:type="notation:FontStyle"/>
            <element href="//0/@process/@activity/@activity/@links/@children.1"/>
            <bendpoints points="[25, -8, -230, 78]$[231, -78, -24, 8]" xsi:type="notation:RelativeBendpoints"/>
        </edges>
        <edges
            source="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0"
            target="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2/@children.0/@children.2" type="4006">
            <children type="6002">
                <layoutConstraint xsi:type="notation:Location" y="40"/>
            </children>
            <styles lineColor="0" xsi:type="notation:ConnectorStyle"/>
            <styles fontName=".SF NS Text" xsi:type="notation:FontStyle"/>
            <element href="//0/@process/@activity/@activity/@activities.2/@activity/@activities.1/@activity/@activity/@links/@children.0"/>
            <bendpoints points="[0, 0, 0, 0]$[0, 0, 0, 0]" xsi:type="notation:RelativeBendpoints"/>
        </edges>
        <edges
            source="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2/@children.0/@children.1"
            target="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2/@children.0/@children.0/@children.0/@children.0/@children.0/@children.3" type="4006">
            <children type="6002">
                <layoutConstraint xsi:type="notation:Location" y="40"/>
            </children>
            <styles lineColor="0" xsi:type="notation:ConnectorStyle"/>
            <styles fontName=".SF NS Text" xsi:type="notation:FontStyle"/>
            <element href="//0/@process/@activity/@activity/@activities.2/@activity/@activities.1/@activity/@activity/@links/@children.1"/>
            <bendpoints points="[0, 0, 0, 0]$[0, 0, 0, 0]" xsi:type="notation:RelativeBendpoints"/>
        </edges>
        <edges
            source="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2/@children.0/@children.0/@children.0/@children.0/@children.0/@children.1"
            target="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2" type="4006">
            <children type="6002">
                <layoutConstraint xsi:type="notation:Location" y="40"/>
            </children>
            <styles lineColor="0" xsi:type="notation:ConnectorStyle"/>
            <styles fontName=".SF NS Text" xsi:type="notation:FontStyle"/>
            <element href="//0/@process/@activity/@activity/@activities.2/@activity/@activities.1/@activity/@activity/@links/@children.2"/>
            <bendpoints points="[0, 0, 0, 0]$[0, 0, 0, 0]" xsi:type="notation:RelativeBendpoints"/>
        </edges>
        <edges
            source="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.3"
            target="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.1" type="4006">
            <children type="6002">
                <layoutConstraint xsi:type="notation:Location" y="40"/>
            </children>
            <styles lineColor="0" xsi:type="notation:ConnectorStyle"/>
            <styles fontName=".SF NS Text" xsi:type="notation:FontStyle"/>
            <element href="//0/@process/@activity/@activity/@links/@children.2"/>
            <bendpoints points="[0, 0, 0, 0]$[0, 0, 0, 0]" xsi:type="notation:RelativeBendpoints"/>
        </edges>
        <edges
            source="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2"
            target="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0" type="4006">
            <children type="6002">
                <layoutConstraint xsi:type="notation:Location" y="40"/>
            </children>
            <styles lineColor="0" xsi:type="notation:ConnectorStyle"/>
            <styles fontName=".SF NS Text" xsi:type="notation:FontStyle"/>
            <element href="//0/@process/@activity/@activity/@activities.2/@activity/@activities.1/@activity/@activity/@links/@children.3"/>
            <bendpoints points="[0, 0, 0, 0]$[0, 0, 0, 0]" xsi:type="notation:RelativeBendpoints"/>
        </edges>
        <edges
            source="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2/@children.0/@children.0/@children.0/@children.0/@children.0/@children.3"
            target="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.2/@children.0/@children.0/@children.0/@children.0/@children.0/@children.1" type="4006">
            <children type="6002">
                <layoutConstraint xsi:type="notation:Location" y="40"/>
            </children>
            <styles lineColor="0" xsi:type="notation:ConnectorStyle"/>
            <styles fontName=".SF NS Text" xsi:type="notation:FontStyle"/>
            <element href="//0/@process/@activity/@activity/@activities.2/@activity/@activities.1/@activity/@activity/@links/@children.4"/>
            <bendpoints points="[0, 0, 0, 0]$[0, 0, 0, 0]" xsi:type="notation:RelativeBendpoints"/>
        </edges>
    </notation:Diagram>
    <tibex:NamespaceRegistry enabled="true">
        <tibex:namespaceItem
            namespace="http://xmlns.example.com/20190722213305PLT" prefix="tns"/>
        <tibex:namespaceItem
            namespace="http://xmlns.example.com/Default/parameters" prefix="tns3"/>
        <tibex:namespaceItem namespace="http://tns.tibco.com/bw/REST" prefix="tns1"/>
        <tibex:namespaceItem
            namespace="http://www.example.org/MovieCatalogMaster" prefix="tns2"/>
        <tibex:namespaceItem
            namespace="http://www.tibco.com/bw/xpath/bw-custom-functions" prefix="bw"/>
        <tibex:namespaceItem
            namespace="http://www.tibco.com/pe/WriteToLogActivitySchema" prefix="tns4"/>
        <tibex:namespaceItem
            namespace="http://www.tibco.com/bw/xslt/custom-functions" prefix="tib"/>
    </tibex:NamespaceRegistry>
    <bpws:import importType="http://www.w3.org/2001/XMLSchema" namespace="http://www.example.org/MovieCatalogMaster"/>
    <bpws:import importType="http://www.w3.org/2001/XMLSchema" namespace="http://tns.tibco.com/bw/REST"/>
    <bpws:import importType="http://www.w3.org/2001/XMLSchema" namespace="http://xmlns.example.com/Default/parameters"/>
    <bpws:import importType="http://www.w3.org/2001/XMLSchema"
        location="../../../Schemas/MovieCatalogMaster.xsd" namespace="http://www.example.org/MovieCatalogMaster"/>
    <bpws:partnerLinks>
        <bpws:partnerLink name="Www-omdbapi-com"
            partnerLinkType="ns1:partnerLinkType" partnerRole="use"
            sca-bpel:ignore="true" sca-bpel:reference="Www-omdbapi-com" sca-bpel:wiredByImpl="false">
            <tibex:ReferenceBinding>
                <tibex:binding>
                    <bwbinding:BWBaseBinding
                        xmlns:bwbinding="http://tns.tibco.com/bw/model/core/bwbinding"
                        xmlns:rest="http://xsd.tns.tibco.com/bw/models/binding/rest"
                        xmlns:sca="http://www.osoa.org/xmlns/sca/1.0"
                        xmlns:scact="http://xsd.tns.tibco.com/amf/models/sca/componentType"
                        xmlns:scaext="http://xsd.tns.tibco.com/amf/models/sca/extensions" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                        <referenceBinding name="Www-omdbapi-com" xsi:type="scact:Reference">
                            <sca:interface.wsdl
                                interface="http://xmlns.example.com/20190722213305PLT#wsdl.interface(Www-omdbapi-com)" scaext:wsdlLocation=""/>
                            <scaext:binding basePath="/"
                                connector="moviecatalogsearch.module.HttpClientResource1"
                                docBasePath="http://localhost:7777/"
                                docResourcePath="Default"
                                name="RestReference" path="/"
                                structuredData="true"
                                technologyVersion="" xsi:type="rest:RestReferenceBinding">
                                <operation httpMethod="GET"
                                    ignoreAdditionalJsonFields="true"
                                    nickname="get-Www-omdbapi-com"
                                    operationName="get"
                                    requestEntityProcessing="chunked" responseStyle="element">
                                    <parameters>
                                    <parameterMapping
                                    dataType="string"
                                    parameterName="i"
                                    parameterType="Query" required="true"/>
                                    <parameterMapping
                                    dataType="string"
                                    parameterName="apikey"
                                    parameterType="Query" required="true"/>
                                    </parameters>
                                    <clientFormat>json</clientFormat>
                                    <clientRequestFormat>json</clientRequestFormat>
                                </operation>
                                <parameters/>
                                <advancedConfig blockingQueueSize="2147483647"/>
                            </scaext:binding>
                        </referenceBinding>
                    </bwbinding:BWBaseBinding>
                </tibex:binding>
            </tibex:ReferenceBinding>
        </bpws:partnerLink>
    </bpws:partnerLinks>
    <bpws:variables>
        <bpws:variable element="ns:ProcessContext"
            name="_processContext" sca-bpel:internal="true"/>
        <bpws:variable element="ns0:OMDBSearchElement" name="Start"
            sca-bpel:internal="true" tibex:parameter="in"/>
        <bpws:variable element="ns0:Movie" name="End-input"
            sca-bpel:internal="true" tibex:parameter="out"/>
        <bpws:variable element="ns0:MovieDetails" name="Mapper-input" sca-bpel:internal="true"/>
        <bpws:variable element="ns0:MovieDetails" name="Mapper" sca-bpel:internal="true"/>
        <bpws:variable name="apiKey" sca-bpel:hotUpdate="false"
            sca-bpel:privateProperty="true" sca-bpel:property="yes"
            tibex:propertySource="apikey" type="xsd:string"/>
        <bpws:variable messageType="ns1:getRequest"
            name="InvokeOmdb-input" sca-bpel:internal="true"/>
        <bpws:variable messageType="ns1:getResponse" name="InvokeOmdb" sca-bpel:internal="true"/>
        <bpws:variable element="ns4:ActivityErrorData"
            name="_error_InvokeOmdb" sca-bpel:internal="true"/>
        <bpws:variable element="ns:ErrorReport" name="_error" sca-bpel:internal="true"/>
        <bpws:variable element="ns5:ActivityInput" name="Log-input" sca-bpel:internal="true"/>
        <bpws:variable element="ns5:ActivityInput" name="Log1-input" sca-bpel:internal="true"/>
        <bpws:variable element="ns6:AccumulatedOutput" name="Accumulate" sca-bpel:internal="true"/>
        <bpws:variable element="ns5:ActivityInput" name="Log2-input" sca-bpel:internal="true"/>
    </bpws:variables>
    <bpws:extensions>
        <bpws:extension mustUnderstand="no" namespace="http://www.eclipse.org/gmf/runtime/1.0.2/notation"/>
        <bpws:extension mustUnderstand="no" namespace="http://www.tibco.com/bw/process/info"/>
        <bpws:extension mustUnderstand="no" namespace="http://docs.oasis-open.org/ns/opencsa/sca-bpel/200801"/>
        <bpws:extension mustUnderstand="no" namespace="http://docs.oasis-open.org/ns/opencsa/sca/200912"/>
        <bpws:extension mustUnderstand="no" namespace="http://ns.tibco.com/bw/property"/>
        <bpws:extension mustUnderstand="no" namespace="http://www.tibco.com/bpel/2007/extensions"/>
    </bpws:extensions>
    <bpws:scope name="scope">
        <bpws:flow name="flow">
            <bpws:links>
                <bpws:link name="StartToIterate" tibex:linkType="SUCCESS"/>
                <bpws:link name="IterateToEnd" tibex:linkType="SUCCESS"/>
                <bpws:link name="LogToEnd" tibex:linkType="SUCCESS"/>
            </bpws:links>
            <bpws:extensionActivity>
                <tibex:receiveEvent createInstance="yes"
                    eventTimeout="0" name="Start"
                    tibex:xpdlId="0dabef06-725d-4d34-826d-48a29137b744"
                    variable="Start" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
                    <bpws:sources>
                        <bpws:source linkName="StartToIterate"/>
                    </bpws:sources>
                    <tibex:eventSource>
                        <tibex:StartEvent xmlns:tibex="http://www.tibco.com/bpel/2007/extensions"/>
                    </tibex:eventSource>
                </tibex:receiveEvent>
            </bpws:extensionActivity>
            <bpws:extensionActivity>
                <tibex:activityExtension inputVariable="End-input"
                    name="End"
                    tibex:xpdlId="36f9c8b6-23af-4667-9b10-a178163a826c" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
                    <bpws:targets>
                        <bpws:target linkName="LogToEnd"/>
                    </bpws:targets>
                    <tibex:inputBindings>
                        <tibex:inputBinding
                            expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns2=&quot;http://www.example.org/MovieCatalogMaster&quot; version=&quot;2.0&quot;>&lt;xsl:param name=&quot;Accumulate&quot;/>&lt;xsl:template name=&quot;End-input&quot; match=&quot;/&quot;>&lt;tns2:Movie>&lt;xsl:for-each select=&quot;$Accumulate/tns2:MovieDetails&quot;>&lt;tns2:Movies>&lt;tns2:MovieDetail>&lt;xsl:if test=&quot;tns2:imdbID&quot;>&lt;tns2:imdbID>&lt;xsl:value-of select=&quot;tns2:imdbID&quot;/>&lt;/tns2:imdbID>&lt;/xsl:if>&lt;xsl:if test=&quot;tns2:Title&quot;>&lt;tns2:Title>&lt;xsl:value-of select=&quot;tns2:Title&quot;/>&lt;/tns2:Title>&lt;/xsl:if>&lt;xsl:if test=&quot;tns2:Year&quot;>&lt;tns2:Year>&lt;xsl:value-of select=&quot;tns2:Year&quot;/>&lt;/tns2:Year>&lt;/xsl:if>&lt;xsl:if test=&quot;tns2:Plot&quot;>&lt;tns2:Plot>&lt;xsl:value-of select=&quot;tns2:Plot&quot;/>&lt;/tns2:Plot>&lt;/xsl:if>&lt;xsl:if test=&quot;tns2:Type&quot;>&lt;tns2:Type>&lt;xsl:value-of select=&quot;tns2:Type&quot;/>&lt;/tns2:Type>&lt;/xsl:if>&lt;/tns2:MovieDetail>&lt;/tns2:Movies>&lt;/xsl:for-each>&lt;/tns2:Movie>&lt;/xsl:template>&lt;/xsl:stylesheet>" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"/>
                    </tibex:inputBindings>
                    <tibex:config>
                        <bwext:BWActivity
                            activityTypeID="bw.internal.end"
                            xmlns:activityconfig="http://tns.tibco.com/bw/model/activityconfig"
                            xmlns:bwext="http://tns.tibco.com/bw/model/core/bwext"
                            xmlns:internalactivities="http://ns.tibco.com/bw/core/internalactivity" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                            <activityConfig>
                                <properties name="config" xsi:type="activityconfig:EMFProperty">
                                    <type href="http://ns.tibco.com/bw/core/internalactivity#//End"/>
                                    <value xsi:type="internalactivities:End"/>
                                </properties>
                            </activityConfig>
                        </bwext:BWActivity>
                    </tibex:config>
                </tibex:activityExtension>
            </bpws:extensionActivity>
            <bpws:scope name="Iterate" tibex:group="iterate" tibex:xpdlId="dd5f6303-2239-4bc1-989b-79f72063fb00">
                <bpws:targets>
                    <bpws:target linkName="StartToIterate"/>
                </bpws:targets>
                <bpws:sources>
                    <bpws:source linkName="IterateToEnd"/>
                </bpws:sources>
                <bpws:sequence name="sequence" tibex:group="iterate">
                    <bpws:assign name="GroupInit"
                        tibex:group="groupInit" validate="no">
                        <bpws:copy>
                            <bpws:from expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"><![CDATA[<?xml version="1.0" encoding="UTF-8"?> <xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:tns="http://tns.tibco.com/bw/palette/internal/accumulateoutput+4721fe34-3f61-4d32-847b-d3abbd59ed04" version="2.0"> <xsl:template name="Accumulate" match="/"> <tns:AccumulatedOutput/></xsl:template> </xsl:stylesheet>]]></bpws:from>
                            <bpws:to variable="Accumulate"/>
                        </bpws:copy>
                    </bpws:assign>
                    <bpws:forEach counterName="index" name="iterate"
                        parallel="no" tibex:group="iterate">
                        <tibex:DesignExpression name="variableList">
                            <tibex:expression
                                expression="$Start/ns0:Search" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xpath2.0"/>
                        </tibex:DesignExpression>
                        <bpws:startCounterValue><![CDATA[1]]></bpws:startCounterValue>
                        <bpws:finalCounterValue expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xpath2.0"><![CDATA[count($Start/ns0:Search)]]></bpws:finalCounterValue>
                        <bpws:scope name="innerScope">
                            <bpws:variables>
                                <bpws:variable
                                    element="ns0:Iterate_Current_Element_Search"
                                    name="currentElement" sca-bpel:internal="true"/>
                            </bpws:variables>
                            <bpws:flow name="flow1">
                                <bpws:links>
                                    <bpws:link name="MapperToGroupEnd" tibex:linkType="SUCCESS"/>
                                    <bpws:link name="GroupStartToget" tibex:linkType="SUCCESS"/>
                                    <bpws:link name="getToMapper" tibex:linkType="SUCCESS"/>
                                    <bpws:link name="Log1ToMapper" tibex:linkType="SUCCESS"/>
                                    <bpws:link name="Log2ToInvokeOmdb" tibex:linkType="SUCCESS"/>
                                </bpws:links>
                                <bpws:assign name="GroupStart"
                                    tibex:group="groupStart"
                                    tibex:xpdlId="d3db12f4-5e5d-4a93-b95a-c0d017c7164d" validate="no">
                                    <bpws:sources>
                                    <bpws:source linkName="GroupStartToget"/>
                                    </bpws:sources>
                                    <bpws:copy>
                                    <bpws:from expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xpath2.0"><![CDATA[($Start/ns0:Search)[number($index)]]]></bpws:from>
                                    <bpws:to variable="currentElement"/>
                                    </bpws:copy>
                                </bpws:assign>
                                <bpws:extensionActivity>
                                    <tibex:activityExtension
                                    name="Accumulate"
                                    outputVariable="Accumulate"
                                    tibex:group="groupEnd"
                                    tibex:xpdlId="4721fe34-3f61-4d32-847b-d3abbd59ed04" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
                                    <bpws:targets>
                                    <bpws:target linkName="MapperToGroupEnd"/>
                                    </bpws:targets>
                                    <tibex:config>
                                    <bwext:BWActivity
                                    activityTypeID="bw.internal.accumulateend"
                                    xmlns:activityconfig="http://tns.tibco.com/bw/model/activityconfig"
                                    xmlns:bwext="http://tns.tibco.com/bw/model/core/bwext"
                                    xmlns:internalactivities="http://ns.tibco.com/bw/core/internalactivity" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                    <activityConfig>
                                    <properties
                                    name="config" xsi:type="activityconfig:EMFProperty">
                                    <type href="http://ns.tibco.com/bw/core/internalactivity#//AccumulateEnd"/>
                                    <value xsi:type="internalactivities:AccumulateEnd">
                                    <activityNames>Mapper</activityNames>
                                    </value>
                                    </properties>
                                    </activityConfig>
                                    </bwext:BWActivity>
                                    </tibex:config>
                                    </tibex:activityExtension>
                                </bpws:extensionActivity>
                                <bpws:extensionActivity>
                                    <tibex:activityExtension
                                    expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns2=&quot;http://www.example.org/MovieCatalogMaster&quot; version=&quot;2.0&quot;>&#xa;    &lt;xsl:param name=&quot;InvokeOmdb&quot;/>&#xa;    &lt;xsl:template name=&quot;Mapper-input&quot; match=&quot;/&quot;>&#xa;        &lt;xsl:copy-of select=&quot;$InvokeOmdb/item/tns2:MovieDetails&quot;/>&#xa;    &lt;/xsl:template>&#xa;&lt;/xsl:stylesheet>"
                                    expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"
                                    inputVariable="Mapper-input"
                                    name="Mapper"
                                    outputVariable="Mapper"
                                    tibex:xpdlId="11e3ba69-d4e4-4660-aa0e-d59d572d578b" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
                                    <bpws:targets>
                                    <bpws:target linkName="Log1ToMapper"/>
                                    </bpws:targets>
                                    <bpws:sources>
                                    <bpws:source linkName="MapperToGroupEnd"/>
                                    </bpws:sources>
                                    <tibex:inputBindings>
                                    <tibex:inputBinding
                                    expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns2=&quot;http://www.example.org/MovieCatalogMaster&quot; version=&quot;2.0&quot;>&#xa;    &lt;xsl:param name=&quot;InvokeOmdb.item&quot;/>&#xa;    &lt;xsl:template name=&quot;Mapper-input&quot; match=&quot;/&quot;>&#xa;        &lt;xsl:copy-of select=&quot;$InvokeOmdb.item&quot;/>&#xa;    &lt;/xsl:template>&#xa;&lt;/xsl:stylesheet>" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"/>
                                    </tibex:inputBindings>
                                    <tibex:config>
                                    <bwext:BWActivity
                                    activityTypeID="bw.generalactivities.mapper"
                                    version="6.0.0.001"
                                    xmlns:MovieCatalogMaster="http://www.example.org/MovieCatalogMaster"
                                    xmlns:activityconfig="http://tns.tibco.com/bw/model/activityconfig"
                                    xmlns:bwext="http://tns.tibco.com/bw/model/core/bwext"
                                    xmlns:generalactivities="http://ns.tibco.com/bw/palette/generalactivities" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                    <activityConfig>
                                    <properties
                                    name="config" xsi:type="activityconfig:EMFProperty">
                                    <type href="http://ns.tibco.com/bw/palette/generalactivities#//Mapper"/>
                                    <value
                                    inputQName="MovieCatalogMaster:MovieDetails" xsi:type="generalactivities:Mapper"/>
                                    </properties>
                                    </activityConfig>
                                    </bwext:BWActivity>
                                    </tibex:config>
                                    </tibex:activityExtension>
                                </bpws:extensionActivity>
                                <bpws:invoke
                                    inputVariable="InvokeOmdb-input"
                                    name="InvokeOmdb" operation="get"
                                    outputVariable="InvokeOmdb"
                                    partnerLink="Www-omdbapi-com"
                                    portType="ns1:Www-omdbapi-com" tibex:xpdlId="83cc635f-6d61-4b30-8898-711e065cb01e">
                                    <tibex:inputBinding expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0">&lt;?xml version="1.0" encoding="UTF-8"?&gt;
&lt;xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:tns="http://xmlns.example.com/20190722213305PLT" xmlns:tns1="http://tns.tibco.com/bw/REST" xmlns:tns2="http://www.example.org/MovieCatalogMaster" xmlns:tns3="http://xmlns.example.com/Default/parameters" version="2.0"&gt;&lt;xsl:param name="apiKey"/&gt;&lt;xsl:param name="currentElement"/&gt;&lt;xsl:template name="InvokeOmdb-input" match="/"&gt;&lt;tns:getRequest&gt;&lt;httpHeaders&gt;&lt;tns1:httpHeaders/&gt;&lt;/httpHeaders&gt;&lt;parameters&gt;&lt;tns3:Www-omdbapi-comGetParameters&gt;&lt;tns3:i&gt;&lt;xsl:value-of select="$currentElement/tns2:imdbID"/&gt;&lt;/tns3:i&gt;&lt;tns3:apikey&gt;&lt;xsl:value-of select="$apiKey"/&gt;&lt;/tns3:apikey&gt;&lt;/tns3:Www-omdbapi-comGetParameters&gt;&lt;/parameters&gt;&lt;/tns:getRequest&gt;&lt;/xsl:template&gt;&lt;/xsl:stylesheet&gt;</tibex:inputBinding>
                                    <tibex:inputBindings>
                                    <tibex:partBinding
                                    expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns=&quot;http://xmlns.example.com/20190722213305PLT&quot; xmlns:tns1=&quot;http://tns.tibco.com/bw/REST&quot; xmlns:tns2=&quot;http://www.example.org/MovieCatalogMaster&quot; xmlns:tns3=&quot;http://xmlns.example.com/Default/parameters&quot; version=&quot;2.0&quot;>&#xa;    &lt;xsl:template name=&quot;InvokeOmdb-input&quot; match=&quot;/&quot;>&#xa;        &lt;tns1:httpHeaders/>&#xa;    &lt;/xsl:template>&#xa;&lt;/xsl:stylesheet>" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"/>
                                    <tibex:partBinding
                                    expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns=&quot;http://xmlns.example.com/20190722213305PLT&quot; xmlns:tns1=&quot;http://tns.tibco.com/bw/REST&quot; xmlns:tns2=&quot;http://www.example.org/MovieCatalogMaster&quot; xmlns:tns3=&quot;http://xmlns.example.com/Default/parameters&quot; version=&quot;2.0&quot;>&#xa;    &lt;xsl:param name=&quot;apiKey&quot;/>&#xa;    &lt;xsl:param name=&quot;currentElement&quot;/>&#xa;    &lt;xsl:template name=&quot;InvokeOmdb-input&quot; match=&quot;/&quot;>&#xa;        &lt;tns3:Www-omdbapi-comGetParameters>&#xa;            &lt;tns3:i>&#xa;                &lt;xsl:value-of select=&quot;$currentElement/tns2:imdbID&quot;/>&#xa;            &lt;/tns3:i>&#xa;            &lt;tns3:apikey>&#xa;                &lt;xsl:value-of select=&quot;$apiKey&quot;/>&#xa;            &lt;/tns3:apikey>&#xa;        &lt;/tns3:Www-omdbapi-comGetParameters>&#xa;    &lt;/xsl:template>&#xa;&lt;/xsl:stylesheet>" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"/>
                                    </tibex:inputBindings>
                                    <bpws:targets>
                                    <bpws:target linkName="Log2ToInvokeOmdb"/>
                                    </bpws:targets>
                                    <bpws:sources>
                                    <bpws:source linkName="getToMapper"/>
                                    </bpws:sources>
                                </bpws:invoke>
                                <bpws:extensionActivity>
                                    <tibex:activityExtension
                                    expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns4=&quot;http://www.tibco.com/pe/WriteToLogActivitySchema&quot; xmlns:tns2=&quot;http://www.example.org/MovieCatalogMaster&quot; xmlns:tib=&quot;http://www.tibco.com/bw/xslt/custom-functions&quot; version=&quot;2.0&quot;>&lt;xsl:param name=&quot;InvokeOmdb&quot;/>&lt;xsl:template name=&quot;Log1-input&quot; match=&quot;/&quot;>&lt;tns4:ActivityInput>&lt;message>&lt;xsl:value-of select=&quot;concat(&amp;quot;Sort Movies-Found Movie Details &amp;quot;, tib:render-xml($InvokeOmdb/item/tns2:MovieDetails))&quot;/>&lt;/message>&lt;/tns4:ActivityInput>&lt;/xsl:template>&lt;/xsl:stylesheet>"
                                    expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"
                                    inputVariable="Log1-input"
                                    name="Log1"
                                    tibex:xpdlId="fd02ed95-23b0-4c38-a840-9a6435d0571e" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
                                    <bpws:targets>
                                    <bpws:target linkName="getToMapper"/>
                                    </bpws:targets>
                                    <bpws:sources>
                                    <bpws:source linkName="Log1ToMapper"/>
                                    </bpws:sources>
                                    <tibex:inputBindings>
                                    <tibex:inputBinding
                                    expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns4=&quot;http://www.tibco.com/pe/WriteToLogActivitySchema&quot; xmlns:tns2=&quot;http://www.example.org/MovieCatalogMaster&quot; xmlns:tib=&quot;http://www.tibco.com/bw/xslt/custom-functions&quot; version=&quot;2.0&quot;>&#xa;    &lt;xsl:param name=&quot;InvokeOmdb.item&quot;/>&#xa;    &lt;xsl:template name=&quot;Log1-input&quot; match=&quot;/&quot;>&#xa;        &lt;tns4:ActivityInput>&#xa;            &lt;message>&#xa;                &lt;xsl:value-of select=&quot;concat(&amp;quot;Sort Movies-Found Movie Details &amp;quot;, tib:render-xml($InvokeOmdb.item))&quot;/>&#xa;            &lt;/message>&#xa;        &lt;/tns4:ActivityInput>&#xa;    &lt;/xsl:template>&#xa;&lt;/xsl:stylesheet>" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"/>
                                    </tibex:inputBindings>
                                    <tibex:config>
                                    <bwext:BWActivity
                                    activityTypeID="bw.generalactivities.log"
                                    version="6.0.0.001"
                                    xmlns:activityconfig="http://tns.tibco.com/bw/model/activityconfig"
                                    xmlns:bwext="http://tns.tibco.com/bw/model/core/bwext"
                                    xmlns:generalactivities="http://ns.tibco.com/bw/palette/generalactivities" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                    <activityConfig>
                                    <properties
                                    name="config" xsi:type="activityconfig:EMFProperty">
                                    <type href="http://ns.tibco.com/bw/palette/generalactivities#//Log"/>
                                    <value
                                    role="Info"
                                    suppressJobInfo="true" xsi:type="generalactivities:Log"/>
                                    </properties>
                                    </activityConfig>
                                    </bwext:BWActivity>
                                    </tibex:config>
                                    </tibex:activityExtension>
                                </bpws:extensionActivity>
                                <bpws:extensionActivity>
                                    <tibex:activityExtension
                                    inputVariable="Log2-input"
                                    name="Log2"
                                    tibex:xpdlId="fec10104-b13a-4e97-bdc9-8e534ba43750" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
                                    <bpws:targets>
                                    <bpws:target linkName="GroupStartToget"/>
                                    </bpws:targets>
                                    <bpws:sources>
                                    <bpws:source linkName="Log2ToInvokeOmdb"/>
                                    </bpws:sources>
                                    <tibex:inputBindings>
                                    <tibex:inputBinding
                                    expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns4=&quot;http://www.tibco.com/pe/WriteToLogActivitySchema&quot; version=&quot;2.0&quot;>&lt;xsl:template name=&quot;Log2-input&quot; match=&quot;/&quot;>&lt;tns4:ActivityInput>&lt;message>&lt;xsl:value-of select=&quot;&amp;quot;Inside Iterate&amp;quot;&quot;/>&lt;/message>&lt;/tns4:ActivityInput>&lt;/xsl:template>&lt;/xsl:stylesheet>" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"/>
                                    </tibex:inputBindings>
                                    <tibex:config>
                                    <bwext:BWActivity
                                    activityTypeID="bw.generalactivities.log"
                                    version="6.0.0.001"
                                    xmlns:activityconfig="http://tns.tibco.com/bw/model/activityconfig"
                                    xmlns:bwext="http://tns.tibco.com/bw/model/core/bwext"
                                    xmlns:generalactivities="http://ns.tibco.com/bw/palette/generalactivities" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                                    <activityConfig>
                                    <properties
                                    name="config" xsi:type="activityconfig:EMFProperty">
                                    <type href="http://ns.tibco.com/bw/palette/generalactivities#//Log"/>
                                    <value
                                    role="Info"
                                    suppressJobInfo="true" xsi:type="generalactivities:Log"/>
                                    </properties>
                                    </activityConfig>
                                    </bwext:BWActivity>
                                    </tibex:config>
                                    </tibex:activityExtension>
                                </bpws:extensionActivity>
                            </bpws:flow>
                        </bpws:scope>
                    </bpws:forEach>
                </bpws:sequence>
            </bpws:scope>
            <bpws:extensionActivity>
                <tibex:activityExtension inputVariable="Log-input"
                    name="Log"
                    tibex:xpdlId="fe8d17ac-732b-4c15-8301-28b9db31e532" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
                    <bpws:targets>
                        <bpws:target linkName="IterateToEnd"/>
                    </bpws:targets>
                    <bpws:sources>
                        <bpws:source linkName="LogToEnd"/>
                    </bpws:sources>
                    <tibex:inputBindings>
                        <tibex:inputBinding
                            expression="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xa;&lt;xsl:stylesheet xmlns:xsl=&quot;http://www.w3.org/1999/XSL/Transform&quot; xmlns:tns4=&quot;http://www.tibco.com/pe/WriteToLogActivitySchema&quot; xmlns:tib=&quot;http://www.tibco.com/bw/xslt/custom-functions&quot; version=&quot;2.0&quot;>&lt;xsl:param name=&quot;Accumulate&quot;/>&lt;xsl:template name=&quot;Log-input&quot; match=&quot;/&quot;>&lt;tns4:ActivityInput>&lt;message>&lt;xsl:value-of select=&quot;concat(&amp;quot;Sort Movies Response&amp;quot;, tib:render-xml($Accumulate))&quot;/>&lt;/message>&lt;/tns4:ActivityInput>&lt;/xsl:template>&lt;/xsl:stylesheet>" expressionLanguage="urn:oasis:names:tc:wsbpel:2.0:sublang:xslt1.0"/>
                    </tibex:inputBindings>
                    <tibex:config>
                        <bwext:BWActivity
                            activityTypeID="bw.generalactivities.log"
                            version="6.0.0.001"
                            xmlns:activityconfig="http://tns.tibco.com/bw/model/activityconfig"
                            xmlns:bwext="http://tns.tibco.com/bw/model/core/bwext"
                            xmlns:generalactivities="http://ns.tibco.com/bw/palette/generalactivities" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                            <activityConfig>
                                <properties name="config" xsi:type="activityconfig:EMFProperty">
                                    <type href="http://ns.tibco.com/bw/palette/generalactivities#//Log"/>
                                    <value role="Info"
                                    suppressJobInfo="true" xsi:type="generalactivities:Log"/>
                                </properties>
                            </activityConfig>
                        </bwext:BWActivity>
                    </tibex:config>
                </tibex:activityExtension>
            </bpws:extensionActivity>
        </bpws:flow>
    </bpws:scope>
</bpws:process>
