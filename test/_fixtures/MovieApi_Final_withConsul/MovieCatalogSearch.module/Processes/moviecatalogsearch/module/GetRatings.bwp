<?xml version="1.0" encoding="UTF-8"?>
<bpws:process exitOnStandardFault="no"
    name="moviecatalogsearch.module.GetRatings"
    suppressJoinFailure="yes"
    targetNamespace="http://xmlns.example.com/20190723081807"
    xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
    xmlns:info="http://www.tibco.com/bw/process/info"
    xmlns:ns="http://www.tibco.com/pe/EngineTypes"
    xmlns:sca="http://docs.oasis-open.org/ns/opencsa/sca/200912"
    xmlns:sca-bpel="http://docs.oasis-open.org/ns/opencsa/sca-bpel/200801"
    xmlns:tibex="http://www.tibco.com/bpel/2007/extensions"
    xmlns:tibprop="http://ns.tibco.com/bw/property" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <tibex:Types>
        <xs:schema attributeFormDefault="unqualified"
            elementFormDefault="qualified"
            targetNamespace="http://www.tibco.com/pe/EngineTypes"
            xmlns:tns="http://www.tibco.com/pe/EngineTypes" xmlns:xs="http://www.w3.org/2001/XMLSchema">
            <xs:complexType block="extension restriction"
                final="extension restriction" name="ErrorReport">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="StackTrace" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Msg" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="FullClass" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Class" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ProcessStack" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="MsgCode" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Data" type="tns:anydata"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="OptionalErrorReport">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="StackTrace" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Msg" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="FullClass" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Class" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="ProcessStack" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="MsgCode" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Data" type="tns:anydata"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="FaultDetail">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ActivityName" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0" name="Data" type="tns:anydata"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Msg" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="MsgCode" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ProcessStack" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="StackTrace" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="FullClass" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="Class" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="ProcessContext">
                <xs:sequence>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="JobId" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ApplicationName" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="EngineName" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" name="ProcessInstanceId" type="xs:string"/>
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" minOccurs="0"
                        name="CustomJobId" type="xs:string"/>
                    <!--xs:element name="RestartedFromCheckpoint" form="unqualified" block="extension restriction substitution" type="xs:boolean"/-->
                    <xs:element
                        block="extension restriction substitution"
                        form="unqualified" maxOccurs="unbounded"
                        minOccurs="0" name="TrackingInfo" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType block="extension restriction"
                final="extension restriction" name="anydata">
                <xs:sequence>
                    <xs:any namespace="##any" processContents="lax"/>
                </xs:sequence>
            </xs:complexType>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="OptionalErrorReport" type="tns:OptionalErrorReport"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="ErrorReport" type="tns:ErrorReport"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="FaultDetail" type="tns:FaultDetail"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="ProcessContext" type="tns:ProcessContext"/>
            <xs:element block="extension restriction substitution"
                final="extension restriction" name="CorrelationValue" type="xs:string"/>
        </xs:schema>
        <schema attributeFormDefault="unqualified"
            elementFormDefault="unqualified"
            targetNamespace="http://schemas.tibco.com/bw/pe/plugin/5.0/exceptions"
            version="" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.tibco.com/bw/pe/plugin/5.0/exceptions">
            <complexType name="ActivityExceptionType">
                <sequence>
                    <element name="msg" type="string"/>
                    <element minOccurs="0" name="msgCode" type="string"/>
                </sequence>
            </complexType>
            <element name="ActivityException" type="tns:ActivityExceptionType"/>
            <complexType name="ActivityTimedOutExceptionType">
                <complexContent>
                    <extension base="tns:ActivityExceptionType"/>
                </complexContent>
            </complexType>
            <element name="ActivityTimedOutException" type="tns:ActivityTimedOutExceptionType"/>
            <complexType name="DuplicateKeyExceptionType">
                <complexContent>
                    <extension base="tns:ActivityExceptionType">
                        <sequence>
                            <element name="duplicateKey" type="string"/>
                            <element minOccurs="0" name="previousJobID" type="string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>
            <element name="DuplicateKeyException" type="tns:DuplicateKeyExceptionType"/>
        </schema>
    </tibex:Types>
    <tibex:ProcessInfo callable="true" createdBy="awagle"
        createdOn="Tue Jul 23 08:18:07 IST 2019" description=""
        extraErrorVars="true" modifiers="public"
        productVersion="2.4.4 V34 2019-05-22" scalable="true"
        singleton="true" stateless="true" type="IT"/>
    <tibex:ProcessInterface context="" input="" output=""/>
    <tibex:ProcessTemplateConfigurations/>
    <notation:Diagram measurementUnit="Pixel" type="BWProcess"
        xmlns:bwnotation="http://tns.tibco.com/bw/runtime/BWNotation"
        xmlns:notation="http://www.eclipse.org/gmf/runtime/1.0.2/notation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <children type="2001">
            <children type="5001"/>
            <children type="3002">
                <styles xsi:type="notation:SortingStyle"/>
                <styles xsi:type="notation:FilteringStyle"/>
            </children>
            <children type="3003">
                <styles xsi:type="notation:SortingStyle"/>
                <styles xsi:type="notation:FilteringStyle"/>
            </children>
            <children type="3004">
                <children type="4018">
                    <children type="3018">
                        <children type="4020">
                            <children type="3020">
                                <children type="4005">
                                    <children type="3007">
                                    <children type="4002">
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.0"/>
                                    <layoutConstraint x="2"
                                    xsi:type="notation:Bounds" y="2"/>
                                    </children>
                                    <children type="4002">
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <children type="4017">
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <layoutConstraint xsi:type="notation:Bounds"/>
                                    </children>
                                    <styles
                                    fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles
                                    backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <element href="//0/@process/@activity/@activity/@activities.1"/>
                                    <layoutConstraint x="379"
                                    xsi:type="notation:Bounds" y="2"/>
                                    </children>
                                    <styles xsi:type="notation:DrawerStyle"/>
                                    <styles xsi:type="notation:SortingStyle"/>
                                    <styles xsi:type="notation:FilteringStyle"/>
                                    <element href="//0/@process/@activity/@activity"/>
                                    </children>
                                    <styles fontName=".SF NS Text"
                                    lineColor="0" xsi:type="notation:ShapeStyle"/>
                                    <styles backgroundColor="16777215"
                                    gradientEndColor="50431"
                                    gradientStartColor="16777215" xsi:type="bwnotation:BackgroundStyle"/>
                                    <styles xsi:type="bwnotation:ResizingStyle"/>
                                    <element href="//0/@process/@activity/@activity"/>
                                    <layoutConstraint height="384"
                                    width="515" xsi:type="notation:Bounds"/>
                                </children>
                                <styles xsi:type="notation:SortingStyle"/>
                                <styles xsi:type="notation:FilteringStyle"/>
                            </children>
                            <styles fontName=".SF NS Text" lineColor="0" xsi:type="notation:ShapeStyle"/>
                            <styles xsi:type="bwnotation:BackgroundStyle"/>
                            <styles xsi:type="bwnotation:ResizingStyle"/>
                            <element href="//0/@process/@activity"/>
                            <layoutConstraint height="365" width="444"
                                x="30" xsi:type="notation:Bounds" y="20"/>
                        </children>
                        <styles xsi:type="notation:SortingStyle"/>
                        <styles xsi:type="notation:FilteringStyle"/>
                        <element href="//0/@process/@activity"/>
                    </children>
                    <styles fontName=".SF NS Text" lineColor="0" xsi:type="notation:ShapeStyle"/>
                    <styles xsi:type="bwnotation:BackgroundStyle"/>
                    <styles xsi:type="bwnotation:ResizingStyle"/>
                    <element href="//0/@process/@activity"/>
                    <layoutConstraint height="409" width="519" xsi:type="notation:Bounds"/>
                </children>
                <styles xsi:type="notation:SortingStyle"/>
                <styles xsi:type="notation:FilteringStyle"/>
                <element href="//0/@process"/>
            </children>
            <styles fontName=".SF NS Text" lineColor="0" xsi:type="notation:ShapeStyle"/>
            <element href="//0/@process"/>
            <layoutConstraint height="460" width="860" xsi:type="notation:Bounds"/>
        </children>
        <styles xsi:type="notation:DiagramStyle"/>
        <element href="//0"/>
        <edges
            source="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0"
            target="//@children.0/@children.3/@children.0/@children.0/@children.0/@children.0/@children.0/@children.0/@children.1" type="4006">
            <children type="6002">
                <layoutConstraint xsi:type="notation:Location" y="40"/>
            </children>
            <styles lineColor="0" xsi:type="notation:ConnectorStyle"/>
            <styles fontName=".SF NS Text" xsi:type="notation:FontStyle"/>
            <element href="//0/@process/@activity/@activity/@links/@children.0"/>
            <bendpoints points="[0, 0, 0, 0]$[0, 0, 0, 0]" xsi:type="notation:RelativeBendpoints"/>
        </edges>
    </notation:Diagram>
    <bpws:variables>
        <bpws:variable element="ns:ProcessContext"
            name="_processContext" sca-bpel:internal="true"/>
    </bpws:variables>
    <bpws:extensions>
        <bpws:extension mustUnderstand="no" namespace="http://www.eclipse.org/gmf/runtime/1.0.2/notation"/>
        <bpws:extension mustUnderstand="no" namespace="http://www.tibco.com/bw/process/info"/>
        <bpws:extension mustUnderstand="no" namespace="http://docs.oasis-open.org/ns/opencsa/sca-bpel/200801"/>
        <bpws:extension mustUnderstand="no" namespace="http://docs.oasis-open.org/ns/opencsa/sca/200912"/>
        <bpws:extension mustUnderstand="no" namespace="http://ns.tibco.com/bw/property"/>
        <bpws:extension mustUnderstand="no" namespace="http://www.tibco.com/bpel/2007/extensions"/>
    </bpws:extensions>
    <bpws:scope name="scope">
        <bpws:flow name="flow">
            <bpws:links>
                <bpws:link name="StartToEnd" tibex:linkType="SUCCESS"/>
            </bpws:links>
            <bpws:extensionActivity>
                <tibex:receiveEvent createInstance="yes"
                    eventTimeout="0" name="Start"
                    tibex:xpdlId="76afc2dc-48ce-4b7a-9524-6209eb03b706" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
                    <bpws:sources>
                        <bpws:source linkName="StartToEnd"/>
                    </bpws:sources>
                    <tibex:eventSource>
                        <tibex:StartEvent xmlns:tibex="http://www.tibco.com/bpel/2007/extensions"/>
                    </tibex:eventSource>
                </tibex:receiveEvent>
            </bpws:extensionActivity>
            <bpws:extensionActivity>
                <tibex:activityExtension name="End"
                    tibex:xpdlId="5e55295b-ddb0-4be2-8609-7700333593e5" xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
                    <bpws:targets>
                        <bpws:target linkName="StartToEnd"/>
                    </bpws:targets>
                    <tibex:config>
                        <bwext:BWActivity
                            activityTypeID="bw.internal.end"
                            xmlns:activityconfig="http://tns.tibco.com/bw/model/activityconfig"
                            xmlns:bwext="http://tns.tibco.com/bw/model/core/bwext"
                            xmlns:internalactivities="http://ns.tibco.com/bw/core/internalactivity" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                            <activityConfig>
                                <properties name="config" xsi:type="activityconfig:EMFProperty">
                                    <type href="http://ns.tibco.com/bw/core/internalactivity#//End"/>
                                    <value xsi:type="internalactivities:End"/>
                                </properties>
                            </activityConfig>
                        </bwext:BWActivity>
                    </tibex:config>
                </tibex:activityExtension>
            </bpws:extensionActivity>
        </bpws:flow>
    </bpws:scope>
</bpws:process>
