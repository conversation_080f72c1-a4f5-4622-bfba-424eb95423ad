<?xml version="1.0" encoding="UTF-8"?>
<config:ProjectConfig xmlns:config="http://www.tibco.com/XPD/projectConfig">
  <projectDetails id="com.example.moviecatalogsearchmodule">
    <globalDestinations/>
    <edition>bwcf</edition>
  </projectDetails>
  <config:assetTypes id="com.tibco.amx.zion.schema.asset"/>
  <config:assetTypes id="com.tibco.bw.process.asset"/>
  <config:assetTypes id="com.tibco.xpd.asset.wsdl"/>
  <config:assetTypes id="com.tibco.amx.zion.resource.asset"/>
  <config:assetTypes id="com.tibco.bw.core.design.policy.asset"/>
  <config:specialFolders>
    <config:folder id="_AQAhMKyOEemnEIz6kVJMoQ" kind="com.tibco.amx.zion.schema" location="Schemas"/>
    <config:folder id="_AQDkgKyOEemnEIz6kVJMoQ" kind="com.tibco.bw.process.folder" location="Processes"/>
    <config:folder id="_AQELkKyOEemnEIz6kVJMoQ" kind="wsdl" location="Service Descriptors"/>
    <config:folder id="_AQFZsKyOEemnEIz6kVJMoQ" kind="com.tibco.amx.zion.resource" location="Resources"/>
    <config:folder id="_AQGAwKyOEemnEIz6kVJMoQ" kind="com.tibco.amx.zion.policy" location="Policies"/>
    <config:folder id="_AWWZwKyOEemnEIz6kVJMoQ" kind="com.tibco.bw.module.folder" location="META-INF"/>
  </config:specialFolders>
</config:ProjectConfig>