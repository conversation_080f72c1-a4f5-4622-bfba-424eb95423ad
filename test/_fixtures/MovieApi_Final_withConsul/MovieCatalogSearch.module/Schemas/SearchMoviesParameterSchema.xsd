<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns:source="bw.rest" xmlns:tns="http://xmlns.example.com/SearchMovies/parameters" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="http://xmlns.example.com/SearchMovies/parameters">
  <xs:element name="moviesGetParameters">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="1" minOccurs="1" name="searchString" type="xs:string"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>
