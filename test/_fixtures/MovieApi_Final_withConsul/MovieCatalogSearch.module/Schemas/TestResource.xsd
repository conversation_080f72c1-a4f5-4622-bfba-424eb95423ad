<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://tns.tibco.com/bw/json/1563810299003" elementFormDefault="qualified" targetNamespace="http://tns.tibco.com/bw/json/1563810299003">
  <complexType name="TestElementType">
    <sequence>
      <element maxOccurs="1" minOccurs="0" name="Title" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Year" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Rated" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Released" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Runtime" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Genre" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Director" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Writer" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Actors" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Plot" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Language" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Country" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Awards" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Poster" type="string"/>
      <element maxOccurs="unbounded" minOccurs="0" name="Ratings">
        <complexType>
          <sequence>
            <element maxOccurs="1" minOccurs="0" name="Source" type="string"/>
            <element maxOccurs="1" minOccurs="0" name="Value" type="string"/>
          </sequence>
        </complexType>
      </element>
      <element maxOccurs="1" minOccurs="0" name="Metascore" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="imdbRating" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="imdbVotes" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="imdbID" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Type" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="DVD" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="BoxOffice" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Production" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Website" type="string"/>
      <element maxOccurs="1" minOccurs="0" name="Response" type="string"/>
    </sequence>
  </complexType>
  <element name="TestElement" type="tns:TestElementType"/>
</schema>
