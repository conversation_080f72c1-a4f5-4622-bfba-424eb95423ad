<?xml version="1.0" encoding="UTF-8"?>
<packaging:packageUnit xmlns:packaging="http://schemas.tibco.com/tra/model/core/PackagingModel" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <packaging:description></packaging:description>
  <packaging:properties>
    <packaging:property>
      <packaging:name>//MovieCatalogSearch.module//BW.APPNODE.NAME</packaging:name>
      <packaging:type>xsd:string</packaging:type>
      <packaging:visibility>private</packaging:visibility>
      <packaging:scalable>true</packaging:scalable>
      <packaging:overrideValue>false</packaging:overrideValue>
    </packaging:property>
    <packaging:property>
      <packaging:name>//MovieCatalogSearch.module//BW.DEPLOYMENTUNIT.NAME</packaging:name>
      <packaging:type>xsd:string</packaging:type>
      <packaging:visibility>private</packaging:visibility>
      <packaging:scalable>true</packaging:scalable>
      <packaging:overrideValue>false</packaging:overrideValue>
    </packaging:property>
    <packaging:property>
      <packaging:name>//MovieCatalogSearch.module//BW.HOST.NAME</packaging:name>
      <packaging:type>xsd:string</packaging:type>
      <packaging:visibility>private</packaging:visibility>
      <packaging:scalable>true</packaging:scalable>
      <packaging:overrideValue>false</packaging:overrideValue>
    </packaging:property>
    <packaging:property>
      <packaging:name>//MovieCatalogSearch.module//BW.DEPLOYMENTUNIT.VERSION</packaging:name>
      <packaging:type>xsd:string</packaging:type>
      <packaging:visibility>private</packaging:visibility>
      <packaging:scalable>true</packaging:scalable>
      <packaging:overrideValue>false</packaging:overrideValue>
    </packaging:property>
    <packaging:property>
      <packaging:name>//MovieCatalogSearch.module//BW.MODULE.VERSION</packaging:name>
      <packaging:type>xsd:string</packaging:type>
      <packaging:visibility>private</packaging:visibility>
      <packaging:scalable>true</packaging:scalable>
      <packaging:overrideValue>false</packaging:overrideValue>
    </packaging:property>
    <packaging:property>
      <packaging:name>//MovieCatalogSearch.module//BW.CLOUD.PORT</packaging:name>
      <packaging:type>xsd:int</packaging:type>
      <packaging:visibility>private</packaging:visibility>
      <packaging:scalable>true</packaging:scalable>
      <packaging:overrideValue>false</packaging:overrideValue>
    </packaging:property>
    <packaging:property>
      <packaging:name>//MovieCatalogSearch.module//BW.MODULE.NAME</packaging:name>
      <packaging:type>xsd:string</packaging:type>
      <packaging:visibility>private</packaging:visibility>
      <packaging:scalable>true</packaging:scalable>
      <packaging:overrideValue>false</packaging:overrideValue>
    </packaging:property>
    <packaging:property>
      <packaging:name>//MovieCatalogSearch.module//SearchServiceHost</packaging:name>
      <packaging:type>xsd:string</packaging:type>
      <packaging:visibility>public</packaging:visibility>
      <packaging:scalable>true</packaging:scalable>
      <packaging:overrideValue>false</packaging:overrideValue>
    </packaging:property>
    <packaging:property>
      <packaging:name>//MovieCatalogSearch.module//ServicePort</packaging:name>
      <packaging:type>xsd:int</packaging:type>
      <packaging:visibility>public</packaging:visibility>
      <packaging:scalable>true</packaging:scalable>
      <packaging:overrideValue>false</packaging:overrideValue>
    </packaging:property>
    <packaging:property>
      <packaging:name>//MovieCatalogSearch.module//SearchServicePort</packaging:name>
      <packaging:type>xsd:int</packaging:type>
      <packaging:visibility>public</packaging:visibility>
      <packaging:scalable>true</packaging:scalable>
      <packaging:overrideValue>false</packaging:overrideValue>
    </packaging:property>
    <packaging:property>
      <packaging:name>//MovieCatalogSearch.module//apikey</packaging:name>
      <packaging:type>xsd:string</packaging:type>
      <packaging:visibility>public</packaging:visibility>
      <packaging:scalable>true</packaging:scalable>
      <packaging:overrideValue>false</packaging:overrideValue>
    </packaging:property>
    <packaging:property>
      <packaging:name>//MovieCatalogSearch.module//DetailsServiceHost</packaging:name>
      <packaging:type>xsd:string</packaging:type>
      <packaging:visibility>public</packaging:visibility>
      <packaging:scalable>true</packaging:scalable>
      <packaging:overrideValue>false</packaging:overrideValue>
    </packaging:property>
    <packaging:property>
      <packaging:name>//MovieCatalogSearch.module//DetailsServicePort</packaging:name>
      <packaging:type>xsd:int</packaging:type>
      <packaging:visibility>public</packaging:visibility>
      <packaging:scalable>true</packaging:scalable>
      <packaging:overrideValue>false</packaging:overrideValue>
    </packaging:property>
  </packaging:properties>
  <packaging:modules>
    <packaging:module>
      <packaging:symbolicName>MovieCatalogSearch.module</packaging:symbolicName>
      <packaging:technologyType>osgi-bundle,bw-appmodule</packaging:technologyType>
      <packaging:technologyVersion>1.0.0.qualifier</packaging:technologyVersion>
    </packaging:module>
  </packaging:modules>
</packaging:packageUnit>