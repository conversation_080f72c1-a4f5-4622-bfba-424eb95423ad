import { JavaXSDComparator } from '../../src/comparators/java-xsd-comparator';
import { ParsedClass, ParsedField, ComparisonResult, Difference } from '../../src/types';
import * as fs from 'fs';

// Mock file-utils
jest.mock('../../src/utils/file-utils');

describe('JavaXSDComparator', () => {
  let comparator: JavaXSDComparator;

  beforeEach(() => {
    comparator = new JavaXSDComparator();
    jest.clearAllMocks();
  });

  describe('compareWithExistingJava', () => {
    it('should compare parsed classes with existing Java files', () => {
      const parsedClasses: ParsedClass[] = [
        {
          name: 'User',
          namespace: '',
          fields: [
            {
              name: 'firstName',
              type: 'String',
              javaType: 'String',
              isOptional: false,
              isArray: false,
              isComplexType: false
            },
            {
              name: 'age',
              type: 'Integer',
              javaType: 'Integer',
              isOptional: false,
              isArray: false,
              isComplexType: false
            }
          ],
          isRootElement: false
        }
      ];

      const mockJavaContent = `
        package com.example.model;
        
        public class User {
            private String firstName;
            private Integer age;
            private String extraField;
        }
      `;

      const { fileExists, readFileContent } = require('../../src/utils/file-utils');
      fileExists.mockReturnValue(true);
      readFileContent.mockReturnValue(mockJavaContent);

      const results = comparator.compareWithExistingJava(
        parsedClasses,
        '/java/src',
        'com.example.model'
      );

      expect(results).toHaveLength(1);
      expect(results[0].className).toBe('User');
      expect(results[0].differences).toBeInstanceOf(Array);
    });

    it('should handle missing Java files', () => {
      const parsedClasses: ParsedClass[] = [
        {
          name: 'MissingClass',
          namespace: '',
          fields: [],
          isRootElement: false
        }
      ];

      const { fileExists } = require('../../src/utils/file-utils');
      fileExists.mockReturnValue(false);

      const results = comparator.compareWithExistingJava(
        parsedClasses,
        '/java/src',
        'com.example.model'
      );

      expect(results).toHaveLength(1);
      expect(results[0].className).toBe('MissingClass');
      expect(results[0].differences).toHaveLength(1);
      expect(results[0].differences[0].description).toContain('does not exist');
    });
  });

  describe('generateReport', () => {
    it('should generate readable comparison report', () => {
      const comparisonResults: ComparisonResult[] = [
        {
          className: 'User',
          differences: [
            {
              type: 'missing_field',
              field: 'lastName',
              expected: 'String',
              description: 'Field lastName is missing in Java class'
            },
            {
              type: 'type_mismatch',
              field: 'age',
              expected: 'Integer',
              actual: 'String',
              description: 'Type mismatch for field age'
            }
          ]
        },
        {
          className: 'Product',
          differences: []
        }
      ];

      const report = comparator.generateReport(comparisonResults);

      expect(report).toContain('Comparison Report');
      expect(report).toContain('User');
      expect(report).toContain('lastName');
      expect(report).toContain('MISSING FIELD');
      expect(report).toContain('age');
      expect(report).toContain('TYPE MISMATCH');
      expect(report).toContain('Product');
      expect(report).toContain('differences found');
    });

    it('should handle empty comparison results', () => {
      const comparisonResults: ComparisonResult[] = [];

      const report = comparator.generateReport(comparisonResults);

      expect(report).toContain('Total classes analyzed: 0');
    });
  });

  describe('integration tests', () => {
    it('should detect field differences correctly', () => {
      const parsedClasses: ParsedClass[] = [
        {
          name: 'TestClass',
          namespace: '',
          fields: [
            {
              name: 'requiredField',
              type: 'String',
              javaType: 'String',
              isOptional: false,
              isArray: false,
              isComplexType: false
            },
            {
              name: 'optionalField',
              type: 'Integer',
              javaType: 'Integer',
              isOptional: true,
              isArray: false,
              isComplexType: false
            },
            {
              name: 'listField',
              type: 'String',
              javaType: 'String',
              isOptional: false,
              isArray: true,
              isComplexType: false
            }
          ],
          isRootElement: false
        }
      ];

      const mockJavaContent = `
        package com.example.model;
        
        import javax.validation.constraints.NotNull;
        import java.util.List;
        
        public class TestClass {
            @NotNull
            private String requiredField;
            
            private String optionalField; // Wrong type
            
            private String listField; // Should be List<String>
            
            private String extraField; // Not in XSD
        }
      `;

      const { fileExists, readFileContent } = require('../../src/utils/file-utils');
      fileExists.mockReturnValue(true);
      readFileContent.mockReturnValue(mockJavaContent);

      const results = comparator.compareWithExistingJava(
        parsedClasses,
        '/java/src',
        'com.example.model'
      );

      expect(results).toHaveLength(1);
      const result = results[0];
      expect(result.className).toBe('TestClass');
      expect(result.differences.length).toBeGreaterThan(0);
      
      // Should detect type mismatches and extra fields
      const typeMismatches = result.differences.filter(d => d.type === 'type_mismatch');
      const extraFields = result.differences.filter(d => d.type === 'extra_field');
      
      expect(typeMismatches.length).toBeGreaterThanOrEqual(1);
      expect(extraFields.length).toBeGreaterThanOrEqual(1);
    });

    it('should handle complex Java class with annotations', () => {
      const parsedClasses: ParsedClass[] = [
        {
          name: 'AnnotatedClass',
          namespace: '',
          fields: [
            {
              name: 'validatedField',
              type: 'String',
              javaType: 'String',
              isOptional: false,
              isArray: false,
              isComplexType: false
            }
          ],
          isRootElement: false
        }
      ];

      const mockJavaContent = `
        package com.example.model;
        
        import lombok.Data;
        import javax.validation.constraints.NotNull;
        import javax.validation.constraints.Size;
        import com.fasterxml.jackson.annotation.JsonProperty;
        
        @Data
        public class AnnotatedClass {
            @NotNull
            @Size(min = 1, max = 100)
            @JsonProperty("validatedField")
            private String validatedField;
        }
      `;

      const { fileExists, readFileContent } = require('../../src/utils/file-utils');
      fileExists.mockReturnValue(true);
      readFileContent.mockReturnValue(mockJavaContent);

      const results = comparator.compareWithExistingJava(
        parsedClasses,
        '/java/src',
        'com.example.model'
      );

      expect(results).toHaveLength(1);
      const result = results[0];
      expect(result.className).toBe('AnnotatedClass');
      // Should have minimal differences since field matches
      expect(result.differences).toBeInstanceOf(Array);
    });

    it('should handle empty Java classes', () => {
      const parsedClasses: ParsedClass[] = [
        {
          name: 'EmptyClass',
          namespace: '',
          fields: [],
          isRootElement: false
        }
      ];

      const mockJavaContent = `
        package com.example.model;
        
        public class EmptyClass {
            // No fields
        }
      `;

      const { fileExists, readFileContent } = require('../../src/utils/file-utils');
      fileExists.mockReturnValue(true);
      readFileContent.mockReturnValue(mockJavaContent);

      const results = comparator.compareWithExistingJava(
        parsedClasses,
        '/java/src',
        'com.example.model'
      );

      expect(results).toHaveLength(1);
      const result = results[0];
      expect(result.className).toBe('EmptyClass');
      expect(result.differences).toHaveLength(0);
    });
  });

  describe('edge cases', () => {
    it('should handle malformed Java files', () => {
      const parsedClasses: ParsedClass[] = [
        {
          name: 'BadClass',
          namespace: '',
          fields: [{
            name: 'field1',
            type: 'String',
            javaType: 'String',
            isOptional: false,
            isArray: false,
            isComplexType: false
          }],
          isRootElement: false
        }
      ];

      const malformedJavaContent = `
        This is not valid Java code
        Missing proper class structure
      `;

      const { fileExists, readFileContent } = require('../../src/utils/file-utils');
      fileExists.mockReturnValue(true);
      readFileContent.mockReturnValue(malformedJavaContent);

      const results = comparator.compareWithExistingJava(
        parsedClasses,
        '/java/src',
        'com.example.model'
      );

      expect(results).toHaveLength(1);
      const result = results[0];
      expect(result.className).toBe('BadClass');
      // Should have differences since fields can't be properly parsed
      expect(result.differences).toBeInstanceOf(Array);
    });

    it('should handle multiple classes with same names', () => {
      const parsedClasses: ParsedClass[] = [
        {
          name: 'DuplicateClass',
          namespace: 'namespace1',
          fields: [],
          isRootElement: false
        },
        {
          name: 'DuplicateClass',
          namespace: 'namespace2',
          fields: [],
          isRootElement: false
        }
      ];

      const mockJavaContent = `
        package com.example.model;
        public class DuplicateClass {}
      `;

      const { fileExists, readFileContent } = require('../../src/utils/file-utils');
      fileExists.mockReturnValue(true);
      readFileContent.mockReturnValue(mockJavaContent);

      const results = comparator.compareWithExistingJava(
        parsedClasses,
        '/java/src',
        'com.example.model'
      );

      expect(results).toHaveLength(2);
      expect(results[0].className).toBe('DuplicateClass');
      expect(results[1].className).toBe('DuplicateClass');
    });
  });
});
