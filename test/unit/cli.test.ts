import { XSDParser } from '../../src/parsers/xsd-parser';
import { JavaCodeGenerator } from '../../src/generators/java-generator';
import { JavaXSDComparator } from '../../src/comparators/java-xsd-comparator';
import { ParsedClass, JavaGenerationOptions } from '../../src/types';

// Mock dependencies
jest.mock('../../src/utils/file-utils');
jest.mock('../../src/parsers/xsd-parser');
jest.mock('../../src/generators/java-generator');
jest.mock('../../src/comparators/java-xsd-comparator');

describe('CLI Components Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('XSD to Java generation workflow', () => {
    it('should complete full workflow from XSD parsing to Java generation', async () => {
      const mockParsedClasses: ParsedClass[] = [
        {
          name: 'User',
          namespace: 'http://example.com/schema',
          fields: [{
            name: 'name',
            type: 'String',
            javaType: 'String',
            isOptional: false,
            isArray: false,
            isComplexType: false
          }],
          isRootElement: false
        }
      ];

      // Mock file utils
      const { findXSDFiles } = require('../../src/utils/file-utils');
      findXSDFiles.mockReturnValue(['/schemas/User.xsd']);

      // Mock XSD parser
      const mockParser = {
        parseXSD: jest.fn().mockResolvedValue(mockParsedClasses)
      };
      (XSDParser as jest.MockedClass<typeof XSDParser>).mockImplementation(() => mockParser as any);

      // Mock Java generator
      const mockGenerator = {
        generateAllClasses: jest.fn(),
        generateClass: jest.fn()
      };
      (JavaCodeGenerator as jest.MockedClass<typeof JavaCodeGenerator>).mockImplementation(() => mockGenerator as any);

      // Simulate workflow
      const inputDir = '/schemas';
      const options: JavaGenerationOptions = {
        packageName: 'com.example.model',
        outputDir: '/output',
        useLombok: true,
        useJacksonAnnotations: true,
        useJSR303Validation: true
      };

      // Step 1: Find XSD files
      const xsdFiles = findXSDFiles(inputDir);
      expect(xsdFiles).toHaveLength(1);

      // Step 2: Parse XSD files
      const parser = new XSDParser();
      const parsedClasses = await parser.parseXSD(xsdFiles[0]);
      expect(mockParser.parseXSD).toHaveBeenCalledWith('/schemas/User.xsd');
      expect(parsedClasses).toEqual(mockParsedClasses);

      // Step 3: Generate Java classes
      const generator = new JavaCodeGenerator(options);
      generator.generateAllClasses(parsedClasses);
      expect(mockGenerator.generateAllClasses).toHaveBeenCalledWith(mockParsedClasses);
    });

    it('should handle comparison workflow', async () => {
      const mockParsedClasses: ParsedClass[] = [
        {
          name: 'User',
          namespace: '',
          fields: [{
            name: 'name',
            type: 'String',
            javaType: 'String',
            isOptional: false,
            isArray: false,
            isComplexType: false
          }],
          isRootElement: false
        }
      ];

      const mockComparisonResults = [{
        className: 'User',
        differences: []
      }];

      // Mock XSD parsing
      const { findXSDFiles } = require('../../src/utils/file-utils');
      findXSDFiles.mockReturnValue(['/schemas/User.xsd']);

      const mockParser = {
        parseXSD: jest.fn().mockResolvedValue(mockParsedClasses)
      };
      (XSDParser as jest.MockedClass<typeof XSDParser>).mockImplementation(() => mockParser as any);

      // Mock comparison
      const mockComparator = {
        compareWithExistingJava: jest.fn().mockReturnValue(mockComparisonResults),
        generateReport: jest.fn().mockReturnValue('Comparison Report')
      };
      (JavaXSDComparator as jest.MockedClass<typeof JavaXSDComparator>).mockImplementation(() => mockComparator as any);

      // Simulate comparison workflow
      const inputDir = '/schemas';
      const javaDir = '/java/src';
      const packageName = 'com.example.model';

      // Step 1: Parse XSD files
      const xsdFiles = findXSDFiles(inputDir);
      const parser = new XSDParser();
      const parsedClasses = await parser.parseXSD(xsdFiles[0]);

      // Step 2: Compare with existing Java
      const comparator = new JavaXSDComparator();
      const results = comparator.compareWithExistingJava(parsedClasses, javaDir, packageName);
      const report = comparator.generateReport(results);

      expect(mockComparator.compareWithExistingJava).toHaveBeenCalledWith(
        mockParsedClasses, 
        javaDir, 
        packageName
      );
      expect(mockComparator.generateReport).toHaveBeenCalledWith(mockComparisonResults);
      expect(report).toBe('Comparison Report');
    });
  });

  describe('error handling in workflows', () => {
    it('should handle XSD parsing errors', async () => {
      const { findXSDFiles } = require('../../src/utils/file-utils');
      findXSDFiles.mockReturnValue(['/schemas/Invalid.xsd']);

      const mockParser = {
        parseXSD: jest.fn().mockRejectedValue(new Error('Invalid XSD format'))
      };
      (XSDParser as jest.MockedClass<typeof XSDParser>).mockImplementation(() => mockParser as any);

      const parser = new XSDParser();
      
      await expect(parser.parseXSD('/schemas/Invalid.xsd')).rejects.toThrow('Invalid XSD format');
    });

    it('should handle missing XSD files', () => {
      const { findXSDFiles } = require('../../src/utils/file-utils');
      findXSDFiles.mockImplementation(() => {
        throw new Error('Directory does not exist');
      });

      expect(() => findXSDFiles('/nonexistent')).toThrow('Directory does not exist');
    });

    it('should handle empty XSD directory', () => {
      const { findXSDFiles } = require('../../src/utils/file-utils');
      findXSDFiles.mockReturnValue([]);

      const xsdFiles = findXSDFiles('/empty');
      expect(xsdFiles).toHaveLength(0);
    });
  });

  describe('configuration validation', () => {
    it('should validate Java generation options', () => {
      const validOptions: JavaGenerationOptions = {
        packageName: 'com.example.model',
        outputDir: '/output',
        useLombok: true,
        useJacksonAnnotations: true,
        useJSR303Validation: true,
        includeConstructors: true,
        includeToString: true
      };

      expect(() => new JavaCodeGenerator(validOptions)).not.toThrow();
    });

    it('should handle invalid package names', () => {
      const invalidOptions: JavaGenerationOptions = {
        packageName: '', // Invalid empty package name
        outputDir: '/output'
      };

      // The generator should handle invalid package names gracefully
      expect(() => new JavaCodeGenerator(invalidOptions)).not.toThrow();
    });
  });

  describe('component integration', () => {
    it('should integrate parser and generator correctly', async () => {
      const mockParsedClasses: ParsedClass[] = [
        {
          name: 'ComplexType',
          namespace: 'http://example.com/schema',
          fields: [
            {
              name: 'stringField',
              type: 'String',
              javaType: 'String',
              isOptional: false,
              isArray: false,
              isComplexType: false
            },
            {
              name: 'listField',
              type: 'String',
              javaType: 'String',
              isOptional: false,
              isArray: true,
              isComplexType: false
            },
            {
              name: 'nestedField',
              type: 'NestedType',
              javaType: 'NestedType',
              isOptional: true,
              isArray: false,
              isComplexType: true,
              nestedClass: {
                name: 'NestedType',
                namespace: '',
                fields: [],
                isRootElement: false
              }
            }
          ],
          isRootElement: true
        }
      ];

      const mockParser = {
        parseXSD: jest.fn().mockResolvedValue(mockParsedClasses)
      };
      (XSDParser as jest.MockedClass<typeof XSDParser>).mockImplementation(() => mockParser as any);

      const mockGenerator = {
        generateAllClasses: jest.fn(),
        generateClass: jest.fn()
      };
      (JavaCodeGenerator as jest.MockedClass<typeof JavaCodeGenerator>).mockImplementation(() => mockGenerator as any);

      const options: JavaGenerationOptions = {
        packageName: 'com.example.model',
        outputDir: '/output'
      };

      const parser = new XSDParser();
      const generator = new JavaCodeGenerator(options);

      const parsedClasses = await parser.parseXSD('/schemas/Complex.xsd');
      generator.generateAllClasses(parsedClasses);

      expect(mockParser.parseXSD).toHaveBeenCalledWith('/schemas/Complex.xsd');
      expect(mockGenerator.generateAllClasses).toHaveBeenCalledWith(mockParsedClasses);
    });
  });
});
