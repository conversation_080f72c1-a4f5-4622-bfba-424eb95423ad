import { JavaCodeGenerator } from '../../src/generators/java-generator';
import { ParsedClass, ParsedField, JavaGenerationOptions } from '../../src/types';
import * as fs from 'fs';

// Mock file-utils
jest.mock('../../src/utils/file-utils');

describe('JavaCodeGenerator', () => {
  let generator: JavaCodeGenerator;
  let options: JavaGenerationOptions;

  beforeEach(() => {
    options = {
      packageName: 'com.example.model',
      outputDir: '/output',
      useLombok: true,
      useJacksonAnnotations: true,
      useJSR303Validation: true,
      includeConstructors: false,
      includeToString: false
    };
    generator = new JavaCodeGenerator(options);
    jest.clearAllMocks();
  });

  describe('generateClass', () => {
    it('should generate Java class file', () => {
      const parsedClass: ParsedClass = {
        name: 'User',
        namespace: 'http://example.com/user',
        fields: [
          {
            name: 'firstName',
            type: 'String',
            javaType: 'String',
            isOptional: false,
            isArray: false,
            isComplexType: false
          },
          {
            name: 'age',
            type: 'Integer',
            javaType: 'Integer',
            isOptional: true,
            isArray: false,
            isComplexType: false
          }
        ],
        isRootElement: false
      };

      const { writeFileContent } = require('../../src/utils/file-utils');
      writeFileContent.mockImplementation(() => {});

      generator.generateClass(parsedClass);

      expect(writeFileContent).toHaveBeenCalled();
      const [filePath, content] = writeFileContent.mock.calls[0];
      
      expect(filePath).toContain('User.java');
      expect(content).toContain('package com.example.model;');
      expect(content).toContain('public class User');
    });
  });

  describe('generateAllClasses', () => {
    it('should generate multiple Java classes', () => {
      const classes: ParsedClass[] = [
        {
          name: 'User',
          namespace: '',
          fields: [{
            name: 'name',
            type: 'String',
            javaType: 'String',
            isOptional: false,
            isArray: false,
            isComplexType: false
          }],
          isRootElement: false
        },
        {
          name: 'Product',
          namespace: '',
          fields: [{
            name: 'title',
            type: 'String',
            javaType: 'String',
            isOptional: false,
            isArray: false,
            isComplexType: false
          }],
          isRootElement: false
        }
      ];

      const { writeFileContent } = require('../../src/utils/file-utils');
      writeFileContent.mockImplementation(() => {});

      generator.generateAllClasses(classes);

      expect(writeFileContent).toHaveBeenCalledTimes(2);
    });
  });

  describe('integration tests', () => {
    it('should handle complex nested types', () => {
      const parsedClass: ParsedClass = {
        name: 'Company',
        namespace: '',
        fields: [
          {
            name: 'name',
            type: 'String',
            javaType: 'String',
            isOptional: false,
            isArray: false,
            isComplexType: false
          },
          {
            name: 'employees',
            type: 'Person',
            javaType: 'List<Person>',
            isOptional: false,
            isArray: true,
            isComplexType: true,
            nestedClass: {
              name: 'Person',
              namespace: '',
              fields: [{
                name: 'name',
                type: 'String',
                javaType: 'String',
                isOptional: false,
                isArray: false,
                isComplexType: false
              }],
              isRootElement: false
            }
          }
        ],
        isRootElement: false
      };

      const { writeFileContent } = require('../../src/utils/file-utils');
      writeFileContent.mockImplementation(() => {});

      generator.generateClass(parsedClass);

      expect(writeFileContent).toHaveBeenCalled();
      const [, content] = writeFileContent.mock.calls[0];
      
      expect(content).toContain('Company');
      expect(content).toContain('List<Person> employees');
    });

    it('should handle optional fields', () => {
      const parsedClass: ParsedClass = {
        name: 'OptionalFieldsClass',
        namespace: '',
        fields: [
          {
            name: 'requiredField',
            type: 'String',
            javaType: 'String',
            isOptional: false,
            isArray: false,
            isComplexType: false
          },
          {
            name: 'optionalField',
            type: 'String',
            javaType: 'String',
            isOptional: true,
            isArray: false,
            isComplexType: false
          }
        ],
        isRootElement: false
      };

      const { writeFileContent } = require('../../src/utils/file-utils');
      writeFileContent.mockImplementation(() => {});

      generator.generateClass(parsedClass);

      expect(writeFileContent).toHaveBeenCalled();
      const [, content] = writeFileContent.mock.calls[0];
      
      expect(content).toContain('requiredField');
      expect(content).toContain('optionalField');
    });

    it('should handle array fields', () => {
      const parsedClass: ParsedClass = {
        name: 'ArrayFieldsClass',
        namespace: '',
        fields: [
          {
            name: 'stringArray',
            type: 'String',
            javaType: 'List<String>',
            isOptional: false,
            isArray: true,
            isComplexType: false
          },
          {
            name: 'integerArray',
            type: 'Integer',
            javaType: 'List<Integer>',
            isOptional: false,
            isArray: true,
            isComplexType: false
          }
        ],
        isRootElement: false
      };

      const { writeFileContent } = require('../../src/utils/file-utils');
      writeFileContent.mockImplementation(() => {});

      generator.generateClass(parsedClass);

      expect(writeFileContent).toHaveBeenCalled();
      const [, content] = writeFileContent.mock.calls[0];
      
      expect(content).toContain('List<String> stringArray');
      expect(content).toContain('List<Integer> integerArray');
    });

    it('should respect Lombok configuration', () => {
      const optionsWithoutLombok: JavaGenerationOptions = {
        ...options,
        useLombok: false,
        includeToString: true
      };
      
      generator = new JavaCodeGenerator(optionsWithoutLombok);

      const parsedClass: ParsedClass = {
        name: 'NoLombokClass',
        namespace: '',
        fields: [{
          name: 'value',
          type: 'String',
          javaType: 'String',
          isOptional: false,
          isArray: false,
          isComplexType: false
        }],
        isRootElement: false
      };

      const { writeFileContent } = require('../../src/utils/file-utils');
      writeFileContent.mockImplementation(() => {});

      generator.generateClass(parsedClass);

      expect(writeFileContent).toHaveBeenCalled();
      const [, content] = writeFileContent.mock.calls[0];
      
      // Should not contain Lombok annotations
      expect(content).not.toContain('@Data');
      // Should contain manual getters/setters and toString
      expect(content).toContain('toString()');
    });

    it('should handle root elements', () => {
      const parsedClass: ParsedClass = {
        name: 'RootElement',
        namespace: 'http://example.com',
        fields: [{
          name: 'content',
          type: 'String',
          javaType: 'String',
          isOptional: false,
          isArray: false,
          isComplexType: false
        }],
        isRootElement: true
      };

      const { writeFileContent } = require('../../src/utils/file-utils');
      writeFileContent.mockImplementation(() => {});

      generator.generateClass(parsedClass);

      expect(writeFileContent).toHaveBeenCalled();
      const [, content] = writeFileContent.mock.calls[0];
      
      expect(content).toContain('RootElement');
      // Should handle root element annotations if Jackson is enabled
      if (options.useJacksonAnnotations) {
        expect(content).toContain('@JsonRootName');
      }
    });
  });
});
