import {
  xsdTypeToJavaType,
  capitalizeFirstLetter,
  lowercaseFirstLetter,
  toCamelCase,
  toPascalCase,
  isOptionalField,
  isArrayField,
  generatePackageName,
  isValidJavaIdentifier,
  generateSafeFieldName,
  generateSafeClassName
} from '../../src/utils/type-utils';

describe('TypeUtils', () => {
  describe('xsdTypeToJavaType', () => {
    it('should map basic XSD types to Java types', () => {
      expect(xsdTypeToJavaType('string')).toBe('String');
      expect(xsdTypeToJavaType('int')).toBe('Integer');
      expect(xsdTypeToJavaType('boolean')).toBe('Boolean');
      expect(xsdTypeToJavaType('double')).toBe('Double');
      expect(xsdTypeToJavaType('float')).toBe('Float');
      expect(xsdTypeToJavaType('long')).toBe('Long');
      expect(xsdTypeToJavaType('decimal')).toBe('BigDecimal');
      expect(xsdTypeToJavaType('dateTime')).toBe('LocalDateTime');
      expect(xsdTypeToJavaType('date')).toBe('LocalDate');
      expect(xsdTypeToJavaType('time')).toBe('LocalTime');
    });

    it('should handle prefixed types', () => {
      expect(xsdTypeToJavaType('xs:string')).toBe('String');
      expect(xsdTypeToJavaType('xs:int')).toBe('Integer');
      expect(xsdTypeToJavaType('xs:boolean')).toBe('Boolean');
    });

    it('should handle custom types with namespace', () => {
      expect(xsdTypeToJavaType('tns:CustomType')).toBe('CustomType');
      expect(xsdTypeToJavaType('ns:UserInfo')).toBe('UserInfo');
    });

    it('should capitalize unknown types', () => {
      expect(xsdTypeToJavaType('customType')).toBe('CustomType');
      expect(xsdTypeToJavaType('userInfo')).toBe('UserInfo');
    });
  });

  describe('capitalizeFirstLetter', () => {
    it('should capitalize the first letter', () => {
      expect(capitalizeFirstLetter('hello')).toBe('Hello');
      expect(capitalizeFirstLetter('world')).toBe('World');
      expect(capitalizeFirstLetter('userName')).toBe('UserName');
    });

    it('should handle edge cases', () => {
      expect(capitalizeFirstLetter('')).toBe('');
      expect(capitalizeFirstLetter('a')).toBe('A');
      expect(capitalizeFirstLetter('A')).toBe('A');
    });
  });

  describe('lowercaseFirstLetter', () => {
    it('should lowercase the first letter', () => {
      expect(lowercaseFirstLetter('Hello')).toBe('hello');
      expect(lowercaseFirstLetter('World')).toBe('world');
      expect(lowercaseFirstLetter('UserName')).toBe('userName');
    });

    it('should handle edge cases', () => {
      expect(lowercaseFirstLetter('')).toBe('');
      expect(lowercaseFirstLetter('a')).toBe('a');
      expect(lowercaseFirstLetter('A')).toBe('a');
    });
  });

  describe('toCamelCase', () => {
    it('should convert various formats to camelCase', () => {
      expect(toCamelCase('user_name')).toBe('userName');
      expect(toCamelCase('user-name')).toBe('userName');
      expect(toCamelCase('user name')).toBe('userName');
      expect(toCamelCase('user.name')).toBe('user.name'); // 不处理点号
      expect(toCamelCase('USER_NAME')).toBe('USERNAME'); // 全大写转换
    });

    it('should handle edge cases', () => {
      expect(toCamelCase('')).toBe('');
      expect(toCamelCase('a')).toBe('a');
      expect(toCamelCase('_')).toBe('');
      expect(toCamelCase('user')).toBe('user');
    });
  });

  describe('toPascalCase', () => {
    it('should convert various formats to PascalCase', () => {
      expect(toPascalCase('user_name')).toBe('UserName');
      expect(toPascalCase('user-name')).toBe('UserName');
      expect(toPascalCase('user name')).toBe('UserName');
      expect(toPascalCase('userName')).toBe('UserName');
    });

    it('should handle edge cases', () => {
      expect(toPascalCase('')).toBe('');
      expect(toPascalCase('a')).toBe('A');
      expect(toPascalCase('user')).toBe('User');
    });
  });

  describe('isOptionalField', () => {
    it('should identify optional fields correctly', () => {
      expect(isOptionalField('0')).toBe(true);
      expect(isOptionalField('1')).toBe(false);
      expect(isOptionalField(undefined)).toBe(true);
      expect(isOptionalField('')).toBe(true);
    });
  });

  describe('isArrayField', () => {
    it('should identify array fields correctly', () => {
      expect(isArrayField('unbounded')).toBe(true);
      expect(isArrayField('2')).toBe(true);
      expect(isArrayField('5')).toBe(true);
      expect(isArrayField('1')).toBe(false);
      expect(isArrayField('0')).toBe(false);
      expect(isArrayField(undefined)).toBe(false);
      expect(isArrayField('')).toBe(false);
    });
  });

  describe('generatePackageName', () => {
    it('should generate valid Java package names', () => {
      expect(generatePackageName('com.example')).toBe('com.example');
      expect(generatePackageName('com.example', 'http://example.com/schema'))
        .toBe('com.example.schema');
      expect(generatePackageName('org.test', 'http://test.org/movie-catalog'))
        .toBe('org.test.moviecatalog');
    });
  });

  describe('isValidJavaIdentifier', () => {
    it('should validate Java identifiers correctly', () => {
      expect(isValidJavaIdentifier('userName')).toBe(true);
      expect(isValidJavaIdentifier('_private')).toBe(true);
      expect(isValidJavaIdentifier('user123')).toBe(true);
      expect(isValidJavaIdentifier('User')).toBe(true);
      
      // Invalid identifiers
      expect(isValidJavaIdentifier('123user')).toBe(false);
      expect(isValidJavaIdentifier('user-name')).toBe(false);
      expect(isValidJavaIdentifier('user name')).toBe(false);
      expect(isValidJavaIdentifier('')).toBe(false);
      
      // Java keywords
      expect(isValidJavaIdentifier('class')).toBe(false);
      expect(isValidJavaIdentifier('public')).toBe(false);
      expect(isValidJavaIdentifier('private')).toBe(false);
      expect(isValidJavaIdentifier('int')).toBe(false);
    });
  });

  describe('generateSafeFieldName', () => {
    it('should generate safe field names', () => {
      expect(generateSafeFieldName('user_name')).toBe('userName');
      expect(generateSafeFieldName('User-Name')).toBe('userName');
      expect(generateSafeFieldName('class')).toBe('_class');
      expect(generateSafeFieldName('public')).toBe('_public');
    });
  });

  describe('generateSafeClassName', () => {
    it('should generate safe class names', () => {
      expect(generateSafeClassName('user_info')).toBe('UserInfo');
      expect(generateSafeClassName('movie-catalog')).toBe('MovieCatalog');
      expect(generateSafeClassName('class')).toBe('_Class');
      expect(generateSafeClassName('interface')).toBe('_Interface');
    });
  });
});
