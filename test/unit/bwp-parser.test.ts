import { B<PERSON>PParser } from '../../src/parsers/bwp-parser';
import { ParsedBWPProcess } from '../../src/types';
import * as fs from 'fs';

// Mock file-utils
jest.mock('../../src/utils/file-utils');

describe('BWPParser', () => {
  let parser: BWPParser;

  beforeEach(() => {
    parser = new BWPParser();
    jest.clearAllMocks();
  });

  describe('parseBWP', () => {
    it('should parse BWP file and return ParsedBWPProcess', async () => {
      const mockContent = `<?xml version="1.0" encoding="UTF-8"?>
        <bpws:process exitOnStandardFault="no"
            name="moviecatalogsearch.module.SearchMovies"
            targetNamespace="http://xmlns.example.com/20190722212639"
            xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
            xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
            <tibex:ProcessInfo callable="true" stateless="true" type="IT" modifiers="public"/>
            <tibex:ProcessInterface
                input="{http://xmlns.example.com/SearchMovies/parameters}moviesGetParameters"
                output="{http://www.example.org/MovieCatalogMaster}OMDBSearchElement"/>
            <tibex:NamespaceRegistry enabled="true">
                <tibex:namespaceItem namespace="http://xmlns.example.com/SearchMovies/parameters" prefix="tns2"/>
                <tibex:namespaceItem namespace="http://www.example.org/MovieCatalogMaster" prefix="tns4"/>
            </tibex:NamespaceRegistry>
            <bpws:variables>
                <bpws:variable element="ns3:moviesGetParameters" name="Start"
                    sca-bpel:internal="true" tibex:parameter="in"/>
                <bpws:variable element="ns0:OMDBSearchElement" name="End-input"
                    sca-bpel:internal="true" tibex:parameter="out"/>
            </bpws:variables>
            <bpws:partnerLinks>
                <bpws:partnerLink name="movies" partnerLinkType="ns1:partnerLinkType" partnerRole="use">
                    <tibex:ReferenceBinding>
                        <tibex:binding>
                            <bwbinding:BWBaseBinding xmlns:bwbinding="http://tns.tibco.com/bw/model/core/bwbinding">
                                <referenceBinding name="movies" xsi:type="scact:Reference" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:scact="http://xsd.tns.tibco.com/amf/models/sca/componentType">
                                    <scaext:binding basePath="/" path="/movies"
                                        xsi:type="rest:RestReferenceBinding" xmlns:scaext="http://xsd.tns.tibco.com/amf/models/sca/extensions" xmlns:rest="http://xsd.tns.tibco.com/bw/models/binding/rest">
                                        <operation httpMethod="GET" operationName="get">
                                            <parameters>
                                                <parameterMapping dataType="string"
                                                    parameterName="searchString"
                                                    parameterType="Query" required="true"/>
                                            </parameters>
                                            <clientFormat>json</clientFormat>
                                        </operation>
                                    </scaext:binding>
                                </referenceBinding>
                            </bwbinding:BWBaseBinding>
                        </tibex:binding>
                    </tibex:ReferenceBinding>
                </bpws:partnerLink>
            </bpws:partnerLinks>
            <bpws:scope name="scope">
                <bpws:flow name="flow">
                    <bpws:links>
                        <bpws:link name="StartToget" tibex:linkType="SUCCESS"/>
                        <bpws:link name="getToEnd" tibex:linkType="SUCCESS"/>
                    </bpws:links>
                    <bpws:extensionActivity>
                        <tibex:receiveEvent createInstance="yes" name="Start"
                            tibex:xpdlId="start-id" variable="Start">
                            <bpws:sources>
                                <bpws:source linkName="StartToget"/>
                            </bpws:sources>
                        </tibex:receiveEvent>
                    </bpws:extensionActivity>
                    <bpws:invoke inputVariable="get-input" name="get"
                        operation="get" outputVariable="get"
                        partnerLink="movies" tibex:xpdlId="invoke-id">
                        <bpws:targets>
                            <bpws:target linkName="StartToget"/>
                        </bpws:targets>
                        <bpws:sources>
                            <bpws:source linkName="getToEnd"/>
                        </bpws:sources>
                    </bpws:invoke>
                    <bpws:extensionActivity>
                        <tibex:activityExtension name="End" tibex:xpdlId="end-id">
                            <bpws:targets>
                                <bpws:target linkName="getToEnd"/>
                            </bpws:targets>
                            <tibex:config>
                                <bwext:BWActivity activityTypeID="bw.internal.end" xmlns:bwext="http://tns.tibco.com/bw/model/core/bwext"/>
                            </tibex:config>
                        </tibex:activityExtension>
                    </bpws:extensionActivity>
                </bpws:flow>
            </bpws:scope>
        </bpws:process>`;

      // Mock readFileContent
      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(mockContent);

      const result = await parser.parseBWP('/test/SearchMovies.bwp');

      expect(result).toBeDefined();
      expect(result.name).toBe('SearchMovies');
      expect(result.namespace).toBe('http://xmlns.example.com/20190722212639');
      expect(result.processInfo.callable).toBe(true);
      expect(result.processInfo.stateless).toBe(true);
      expect(result.interface.inputType).toBe('moviesGetParameters');
      expect(result.interface.outputType).toBe('OMDBSearchElement');
    });

    it('should parse process variables correctly', async () => {
      const mockContent = `<?xml version="1.0" encoding="UTF-8"?>
        <bpws:process name="test.Process" targetNamespace="http://test.com"
            xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
            xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
            <tibex:ProcessInfo callable="true"/>
            <tibex:ProcessInterface input="{http://test.com}Input" output="{http://test.com}Output"/>
            <bpws:variables>
                <bpws:variable element="ns:Input" name="Start"
                    sca-bpel:internal="true" tibex:parameter="in"/>
                <bpws:variable element="ns:Output" name="End"
                    sca-bpel:internal="true" tibex:parameter="out"/>
                <bpws:variable messageType="ns:RequestMessage" name="Request"
                    sca-bpel:internal="false"/>
            </bpws:variables>
        </bpws:process>`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(mockContent);

      const result = await parser.parseBWP('/test/process.bwp');

      expect(result.variables).toHaveLength(3);
      
      const startVar = result.variables.find(v => v.name === 'Start');
      expect(startVar).toBeDefined();
      expect(startVar!.type).toBe('element');
      expect(startVar!.dataType).toBe('Input');
      expect(startVar!.isInternal).toBe(true);
      expect(startVar!.parameterType).toBe('in');

      const requestVar = result.variables.find(v => v.name === 'Request');
      expect(requestVar).toBeDefined();
      expect(requestVar!.type).toBe('messageType');
      expect(requestVar!.dataType).toBe('RequestMessage');
      expect(requestVar!.isInternal).toBe(false);
    });

    it('should parse partner links and REST bindings correctly', async () => {
      const mockContent = `<?xml version="1.0" encoding="UTF-8"?>
        <bpws:process name="test.Process" targetNamespace="http://test.com"
            xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
            xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
            <tibex:ProcessInfo callable="true"/>
            <tibex:ProcessInterface input="{http://test.com}Input" output="{http://test.com}Output"/>
            <bpws:partnerLinks>
                <bpws:partnerLink name="testService" partnerLinkType="ns:TestLinkType" partnerRole="use">
                    <tibex:ReferenceBinding>
                        <tibex:binding>
                            <bwbinding:BWBaseBinding xmlns:bwbinding="http://tns.tibco.com/bw/model/core/bwbinding">
                                <referenceBinding name="testService" xsi:type="scact:Reference" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:scact="http://xsd.tns.tibco.com/amf/models/sca/componentType">
                                    <scaext:binding basePath="/api" path="/test"
                                        connector="test.connector"
                                        docBasePath="http://localhost:8080"
                                        xsi:type="rest:RestReferenceBinding" xmlns:scaext="http://xsd.tns.tibco.com/amf/models/sca/extensions" xmlns:rest="http://xsd.tns.tibco.com/bw/models/binding/rest">
                                        <operation httpMethod="POST" operationName="create">
                                            <parameters>
                                                <parameterMapping dataType="string"
                                                    parameterName="id"
                                                    parameterType="Path" required="true"/>
                                                <parameterMapping dataType="string"
                                                    parameterName="filter"
                                                    parameterType="Query" required="false"/>
                                            </parameters>
                                            <clientFormat>json</clientFormat>
                                        </operation>
                                    </scaext:binding>
                                </referenceBinding>
                            </bwbinding:BWBaseBinding>
                        </tibex:binding>
                    </tibex:ReferenceBinding>
                </bpws:partnerLink>
            </bpws:partnerLinks>
        </bpws:process>`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(mockContent);

      const result = await parser.parseBWP('/test/process.bwp');

      expect(result.partnerLinks).toHaveLength(1);
      
      const partnerLink = result.partnerLinks[0];
      expect(partnerLink.name).toBe('testService');
      expect(partnerLink.partnerLinkType).toBe('ns:TestLinkType');
      expect(partnerLink.role).toBe('use');
      
      expect(partnerLink.restBinding).toBeDefined();
      expect(partnerLink.restBinding!.basePath).toBe('/api');
      expect(partnerLink.restBinding!.path).toBe('/test');
      expect(partnerLink.restBinding!.connector).toBe('test.connector');
      
      expect(partnerLink.restBinding!.operations).toHaveLength(1);
      const operation = partnerLink.restBinding!.operations[0];
      expect(operation.name).toBe('create');
      expect(operation.httpMethod).toBe('POST');
      expect(operation.parameters).toHaveLength(2);
      
      const idParam = operation.parameters.find(p => p.name === 'id');
      expect(idParam).toBeDefined();
      expect(idParam!.parameterType).toBe('Path');
      expect(idParam!.required).toBe(true);
    });

    it('should parse activities correctly', async () => {
      const mockContent = `<?xml version="1.0" encoding="UTF-8"?>
        <bpws:process name="test.Process" targetNamespace="http://test.com"
            xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
            xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
            <tibex:ProcessInfo callable="true"/>
            <tibex:ProcessInterface input="{http://test.com}Input" output="{http://test.com}Output"/>
            <bpws:scope name="scope">
                <bpws:flow name="flow">
                    <bpws:extensionActivity>
                        <tibex:receiveEvent createInstance="yes" name="Start"
                            tibex:xpdlId="start-123" variable="StartVar">
                            <bpws:sources>
                                <bpws:source linkName="StartToInvoke"/>
                            </bpws:sources>
                        </tibex:receiveEvent>
                    </bpws:extensionActivity>
                    <bpws:invoke inputVariable="invokeInput" name="CallService"
                        operation="testOp" outputVariable="invokeOutput"
                        partnerLink="testPartner" tibex:xpdlId="invoke-456">
                        <bpws:targets>
                            <bpws:target linkName="StartToInvoke"/>
                        </bpws:targets>
                        <bpws:sources>
                            <bpws:source linkName="InvokeToLog"/>
                        </bpws:sources>
                    </bpws:invoke>
                    <bpws:extensionActivity>
                        <tibex:activityExtension name="LogActivity"
                            tibex:xpdlId="log-789" inputVariable="logInput">
                            <bpws:targets>
                                <bpws:target linkName="InvokeToLog"/>
                            </bpws:targets>
                            <tibex:config>
                                <bwext:BWActivity activityTypeID="bw.generalactivities.log" version="6.0" xmlns:bwext="http://tns.tibco.com/bw/model/core/bwext"/>
                            </tibex:config>
                        </tibex:activityExtension>
                    </bpws:extensionActivity>
                </bpws:flow>
            </bpws:scope>
        </bpws:process>`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(mockContent);

      const result = await parser.parseBWP('/test/process.bwp');

      expect(result.activities).toHaveLength(3);
      
      const startActivity = result.activities.find(a => a.name === 'Start');
      expect(startActivity).toBeDefined();
      expect(startActivity!.type).toBe('start');
      expect(startActivity!.id).toBe('start-123');
      expect(startActivity!.inputVariable).toBe('StartVar');
      expect(startActivity!.links.sources).toContain('StartToInvoke');

      const invokeActivity = result.activities.find(a => a.name === 'CallService');
      expect(invokeActivity).toBeDefined();
      expect(invokeActivity!.type).toBe('invoke');
      expect(invokeActivity!.operation).toBe('testOp');
      expect(invokeActivity!.partnerLink).toBe('testPartner');
      expect(invokeActivity!.links.targets).toContain('StartToInvoke');
      expect(invokeActivity!.links.sources).toContain('InvokeToLog');

      const logActivity = result.activities.find(a => a.name === 'LogActivity');
      expect(logActivity).toBeDefined();
      expect(logActivity!.type).toBe('log');
      expect(logActivity!.config?.activityTypeID).toBe('bw.generalactivities.log');
    });

    it('should extract REST endpoints correctly', async () => {
      const mockContent = `<?xml version="1.0" encoding="UTF-8"?>
        <bpws:process name="test.SearchService" targetNamespace="http://test.com"
            xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
            xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
            <tibex:ProcessInfo callable="true"/>
            <tibex:ProcessInterface
                input="{http://test.com}SearchRequest"
                output="{http://test.com}SearchResponse"/>
            <bpws:partnerLinks>
                <bpws:partnerLink name="searchAPI" partnerLinkType="ns:SearchLinkType" partnerRole="use">
                    <tibex:ReferenceBinding>
                        <tibex:binding>
                            <bwbinding:BWBaseBinding xmlns:bwbinding="http://tns.tibco.com/bw/model/core/bwbinding">
                                <referenceBinding name="searchAPI" xsi:type="scact:Reference" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:scact="http://xsd.tns.tibco.com/amf/models/sca/componentType">
                                    <scaext:binding basePath="/api" path="/search"
                                        xsi:type="rest:RestReferenceBinding" xmlns:scaext="http://xsd.tns.tibco.com/amf/models/sca/extensions" xmlns:rest="http://xsd.tns.tibco.com/bw/models/binding/rest">
                                        <operation httpMethod="GET" operationName="searchMovies">
                                            <parameters>
                                                <parameterMapping dataType="string"
                                                    parameterName="query"
                                                    parameterType="Query" required="true"/>
                                            </parameters>
                                        </operation>
                                    </scaext:binding>
                                </referenceBinding>
                            </bwbinding:BWBaseBinding>
                        </tibex:binding>
                    </tibex:ReferenceBinding>
                </bpws:partnerLink>
            </bpws:partnerLinks>
        </bpws:process>`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(mockContent);

      const result = await parser.parseBWP('/test/process.bwp');

      expect(result.restEndpoints).toHaveLength(1);
      
      const endpoint = result.restEndpoints[0];
      expect(endpoint.path).toBe('/search');
      expect(endpoint.method).toBe('GET');
      expect(endpoint.operationName).toBe('searchMovies');
      expect(endpoint.inputType).toBe('SearchRequest');
      expect(endpoint.outputType).toBe('SearchResponse');
      expect(endpoint.parameters).toHaveLength(1);
      expect(endpoint.parameters[0].name).toBe('query');
    });

    it('should handle parsing errors gracefully', async () => {
      const invalidXml = `This is not valid XML content`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(invalidXml);

      await expect(parser.parseBWP('/test/invalid.bwp')).rejects.toThrow('Failed to parse BWP XML');
    });

    it('should handle missing process node', async () => {
      const xmlWithoutProcess = `<?xml version="1.0" encoding="UTF-8"?>
        <root>
          <someOtherNode/>
        </root>`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(xmlWithoutProcess);

      await expect(parser.parseBWP('/test/noprocess.bwp')).rejects.toThrow('No process node found in BWP file');
    });
  });
});
