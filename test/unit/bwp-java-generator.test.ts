import { BWPJavaGenerator } from '../../src/generators/bwp-java-generator';
import { ParsedBWPProcess, JavaGenerationOptions } from '../../src/types';

describe('BWPJavaGenerator', () => {
  let generator: BWPJavaGenerator;
  let options: JavaGenerationOptions;
  let mockProcess: ParsedBWPProcess;

  beforeEach(() => {
    options = {
      packageName: 'com.example.movies',
      outputDir: '/output',
      useJSR303Validation: true,
      useLombok: false,
      useJacksonAnnotations: true,
      includeConstructors: true,
      includeToString: true
    };

    generator = new BWPJavaGenerator(options);

    mockProcess = {
      name: 'SearchMovies',
      namespace: 'http://xmlns.example.com/20190722212639',
      processInfo: {
        callable: true,
        stateless: true,
        type: 'IT',
        modifiers: 'public'
      },
      interface: {
        inputType: 'moviesGetParameters',
        outputType: 'OMDBSearchElement',
        inputNamespace: 'http://xmlns.example.com/SearchMovies/parameters',
        outputNamespace: 'http://www.example.org/MovieCatalogMaster'
      },
      activities: [
        {
          id: 'start-id',
          name: 'Start',
          type: 'start',
          inputVariable: 'Start',
          links: { sources: ['StartToget'], targets: [] }
        },
        {
          id: 'invoke-id',
          name: 'get',
          type: 'invoke',
          inputVariable: 'get-input',
          outputVariable: 'get',
          partnerLink: 'movies',
          operation: 'get',
          portType: 'ns1:movies',
          links: { sources: ['getToEnd'], targets: ['StartToget'] }
        },
        {
          id: 'end-id',
          name: 'End',
          type: 'end',
          inputVariable: 'End-input',
          links: { sources: [], targets: ['getToEnd'] }
        }
      ],
      variables: [
        {
          name: 'Start',
          type: 'element',
          dataType: 'moviesGetParameters',
          namespace: 'http://xmlns.example.com/SearchMovies/parameters',
          isInternal: true,
          parameterType: 'in'
        },
        {
          name: 'End-input',
          type: 'element',
          dataType: 'OMDBSearchElement',
          namespace: 'http://www.example.org/MovieCatalogMaster',
          isInternal: true,
          parameterType: 'out'
        }
      ],
      partnerLinks: [
        {
          name: 'movies',
          partnerLinkType: 'ns1:partnerLinkType',
          role: 'use',
          restBinding: {
            basePath: '/',
            path: '/movies',
            connector: 'moviecatalogsearch.module.HttpClientResource',
            docBasePath: 'http://localhost:7777/',
            operations: [
              {
                name: 'get',
                httpMethod: 'GET',
                parameters: [
                  {
                    name: 'searchString',
                    dataType: 'string',
                    parameterType: 'Query',
                    required: true
                  }
                ],
                clientFormat: 'json'
              }
            ]
          }
        }
      ],
      restEndpoints: [
        {
          path: '/movies',
          method: 'GET',
          operationName: 'get',
          inputType: 'moviesGetParameters',
          outputType: 'OMDBSearchElement',
          parameters: [
            {
              name: 'searchString',
              dataType: 'string',
              parameterType: 'Query',
              required: true
            }
          ]
        }
      ]
    };
  });

  describe('generateController', () => {
    it('should generate a valid Spring Boot controller class', () => {
      const result = generator.generateController(mockProcess);

      expect(result).toContain('package com.example.movies;');
      expect(result).toContain('@RestController');
      expect(result).toContain('public class SearchMoviesController {');
      expect(result).toContain('@Autowired');
      expect(result).toContain('private SearchMoviesService searchMoviesService;');
      expect(result).toContain('import org.springframework.web.bind.annotation.*');
      expect(result).toContain('import org.springframework.beans.factory.annotation.Autowired');
    });

    it('should generate controller method for GET endpoint', () => {
      const result = generator.generateController(mockProcess);

      expect(result).toContain('@GetMapping("/movies")');
      expect(result).toContain('public ResponseEntity<OMDBSearchElement> get(');
      expect(result).toContain('@RequestParam("searchString") String searchString');
      // 当前实现使用简单的参数，而不是复杂的请求体
      expect(result).toContain('searchMoviesService.get(searchString)');
      expect(result).toContain('return ResponseEntity.ok(result)');
    });

    it('should handle exceptions in controller methods', () => {
      const result = generator.generateController(mockProcess);

      expect(result).toContain('try {');
      expect(result).toContain('} catch (Exception e) {');
      expect(result).toContain('logger.error("Error in get: " + e.getMessage(), e)');
      expect(result).toContain('return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()');
    });

    it('should generate controller for POST endpoint', () => {
      // Modify mock to have POST endpoint
      mockProcess.restEndpoints[0].method = 'POST';
      mockProcess.partnerLinks[0].restBinding!.operations[0].httpMethod = 'POST';

      const result = generator.generateController(mockProcess);

      expect(result).toContain('@PostMapping("/movies")');
      expect(result).toContain('public ResponseEntity<OMDBSearchElement> get(');
    });
  });

  describe('generateService', () => {
    it('should generate a valid Spring Boot service class', () => {
      const result = generator.generateService(mockProcess);

      expect(result).toContain('package com.example.movies;');
      expect(result).toContain('@Service');
      expect(result).toContain('public class SearchMoviesService {');
      expect(result).toContain('private static final Logger logger = LoggerFactory.getLogger(SearchMoviesService.class);');
      expect(result).toContain('@Autowired');
      expect(result).toContain('private RestTemplate restTemplate;');
      expect(result).toContain('import org.springframework.stereotype.Service');
      expect(result).toContain('import org.springframework.web.client.RestTemplate');
    });

    it('should generate service method for GET endpoint', () => {
      const result = generator.generateService(mockProcess);

      expect(result).toContain('public OMDBSearchElement get(');
      expect(result).toContain('String searchString');
      expect(result).toContain('logger.info("Executing get with parameters:');
      expect(result).toContain('restTemplate.getForObject');
      expect(result).toContain('"searchString=" + searchString');
      expect(result).toContain('OMDBSearchElement.class');
    });

    it('should generate service method for POST endpoint', () => {
      // Modify mock to have POST endpoint
      mockProcess.restEndpoints[0].method = 'POST';
      mockProcess.partnerLinks[0].restBinding!.operations[0].httpMethod = 'POST';

      const result = generator.generateService(mockProcess);

      expect(result).toContain('restTemplate.postForObject("/movies", request, OMDBSearchElement.class)');
    });

    it('should handle service method exceptions', () => {
      const result = generator.generateService(mockProcess);

      expect(result).toContain('try {');
      expect(result).toContain('} catch (Exception e) {');
      expect(result).toContain('logger.error("Error in get: " + e.getMessage(), e)');
      expect(result).toContain('throw new RuntimeException("Service call failed", e)');
    });

    it('should generate query parameters for GET requests', () => {
      // Add multiple query parameters
      mockProcess.restEndpoints[0].parameters.push({
        name: 'page',
        dataType: 'int',
        parameterType: 'Query',
        required: false
      });

      const result = generator.generateService(mockProcess);

      expect(result).toContain('"searchString=" + searchString + "&" + "page=" + page');
    });
  });

  describe('type mapping', () => {
    it('should map data types correctly', () => {
      // Create a process with different parameter types
      const processWithTypes: ParsedBWPProcess = {
        ...mockProcess,
        restEndpoints: [
          {
            path: '/test',
            method: 'GET',
            operationName: 'testTypes',
            inputType: 'TestInput',
            outputType: 'TestOutput',
            parameters: [
              { name: 'stringParam', dataType: 'string', parameterType: 'Query', required: true },
              { name: 'intParam', dataType: 'int', parameterType: 'Query', required: true },
              { name: 'boolParam', dataType: 'boolean', parameterType: 'Query', required: true },
              { name: 'dateParam', dataType: 'date', parameterType: 'Query', required: true }
            ]
          }
        ]
      };

      const result = generator.generateController(processWithTypes);

      expect(result).toContain('String stringParam');
      expect(result).toContain('Integer intParam');
      expect(result).toContain('Boolean boolParam');
      expect(result).toContain('LocalDate dateParam');
    });
  });

  describe('naming conventions', () => {
    it('should convert method names to camelCase', () => {
      mockProcess.restEndpoints[0].operationName = 'SearchMoviesByTitle';
      
      const result = generator.generateController(mockProcess);

      expect(result).toContain('public ResponseEntity<OMDBSearchElement> searchMoviesByTitle(');
    });

    it('should convert parameter names to camelCase', () => {
      mockProcess.restEndpoints[0].parameters[0].name = 'search_string';
      
      const result = generator.generateController(mockProcess);

      expect(result).toContain('@RequestParam("search_string") String searchString');
    });
  });

  describe('imports and annotations', () => {
    it('should include necessary imports in controller', () => {
      const result = generator.generateController(mockProcess);

      expect(result).toContain('import org.springframework.web.bind.annotation.*');
      expect(result).toContain('import org.springframework.beans.factory.annotation.Autowired');
      expect(result).toContain('import org.springframework.http.ResponseEntity');
      expect(result).toContain('import org.springframework.http.HttpStatus');
    });

    it('should include necessary imports in service', () => {
      const result = generator.generateService(mockProcess);

      expect(result).toContain('import org.springframework.stereotype.Service');
      expect(result).toContain('import org.springframework.web.client.RestTemplate');
      expect(result).toContain('import org.slf4j.Logger');
      expect(result).toContain('import org.slf4j.LoggerFactory');
    });
  });

  describe('edge cases', () => {
    it('should handle process with no REST endpoints', () => {
      mockProcess.restEndpoints = [];
      
      const controllerResult = generator.generateController(mockProcess);
      const serviceResult = generator.generateService(mockProcess);

      expect(controllerResult).toContain('public class SearchMoviesController {');
      expect(serviceResult).toContain('public class SearchMoviesService {');
      // Should still have basic structure even with no methods
    });

    it('should handle process with void input type', () => {
      mockProcess.interface.inputType = 'void';
      
      const result = generator.generateController(mockProcess);

      // Should not include @RequestBody parameter
      expect(result).not.toContain('@RequestBody');
    });

    it('should handle parameters with no type mapping', () => {
      mockProcess.restEndpoints[0].parameters[0].dataType = 'customType';
      
      const result = generator.generateController(mockProcess);

      // Should default to String
      expect(result).toContain('String searchString');
    });
  });
});
