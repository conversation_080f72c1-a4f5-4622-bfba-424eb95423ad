import { XSDParser } from '../../src/parsers/xsd-parser';
import { ParsedClass, ParsedField } from '../../src/types';
import * as fs from 'fs';

// Mock file-utils
jest.mock('../../src/utils/file-utils');

// Mock fs to avoid actual file operations
jest.mock('fs');

describe('XSDParser', () => {
  let parser: XSDParser;

  beforeEach(() => {
    parser = new XSDParser();
    jest.clearAllMocks();
  });

  describe('parseXSD', () => {
    it('should parse XSD file and return ParsedClass array', async () => {
      const mockContent = `<?xml version="1.0" encoding="UTF-8"?>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
                   targetNamespace="http://example.com/schema">
          <xs:complexType name="UserType">
            <xs:sequence>
              <xs:element name="firstName" type="xs:string"/>
              <xs:element name="lastName" type="xs:string"/>
            </xs:sequence>
          </xs:complexType>
          <xs:element name="User" type="UserType"/>
        </xs:schema>`;

      // Mock readFileContent
      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(mockContent);

      const result = await parser.parseXSD('/test/schema.xsd');

      expect(result).toBeInstanceOf(Array);
      expect(result.length).toBeGreaterThan(0);
      
      // Check that we have parsed classes
      const userClass = result.find(cls => cls.name === 'UserType');
      expect(userClass).toBeDefined();
      expect(userClass!.fields).toHaveLength(2);
      expect(userClass!.fields[0].name).toBe('firstName');
      expect(userClass!.fields[1].name).toBe('lastName');
    });

    it('should handle XSD with only elements', async () => {
      const mockContent = `<?xml version="1.0" encoding="UTF-8"?>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
          <xs:element name="userName" type="xs:string"/>
          <xs:element name="age" type="xs:int"/>
        </xs:schema>`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(mockContent);

      const result = await parser.parseXSD('/test/simple.xsd');

      expect(result).toBeInstanceOf(Array);
      // Elements without complex types might create simple classes
      expect(result.length).toBeGreaterThanOrEqual(0);
    });

    it('should handle nested complex types', async () => {
      const mockContent = `<?xml version="1.0" encoding="UTF-8"?>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
          <xs:complexType name="AddressType">
            <xs:sequence>
              <xs:element name="street" type="xs:string"/>
              <xs:element name="city" type="xs:string"/>
            </xs:sequence>
          </xs:complexType>
          <xs:complexType name="PersonType">
            <xs:sequence>
              <xs:element name="name" type="xs:string"/>
              <xs:element name="address" type="AddressType"/>
            </xs:sequence>
          </xs:complexType>
        </xs:schema>`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(mockContent);

      const result = await parser.parseXSD('/test/nested.xsd');

      expect(result).toBeInstanceOf(Array);
      expect(result.length).toBeGreaterThanOrEqual(2);
      
      const addressType = result.find(cls => cls.name === 'AddressType');
      const personType = result.find(cls => cls.name === 'PersonType');
      
      expect(addressType).toBeDefined();
      expect(personType).toBeDefined();
      
      if (addressType) {
        expect(addressType.fields).toHaveLength(2);
        expect(addressType.fields[0].name).toBe('street');
        expect(addressType.fields[1].name).toBe('city');
      }

      if (personType) {
        expect(personType.fields).toHaveLength(2);
        expect(personType.fields[0].name).toBe('name');
        expect(personType.fields[1].name).toBe('address');
      }
    });

    it('should handle parsing errors gracefully', async () => {
      const invalidXml = `This is not valid XML content`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(invalidXml);

      await expect(parser.parseXSD('/test/invalid.xsd')).rejects.toThrow();
    });

    it('should handle empty schema', async () => {
      const emptySchema = `<?xml version="1.0" encoding="UTF-8"?>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
        </xs:schema>`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(emptySchema);

      const result = await parser.parseXSD('/test/empty.xsd');

      expect(result).toBeInstanceOf(Array);
      expect(result).toHaveLength(0);
    });
  });

  describe('parseXSD integration tests', () => {
    it('should properly set root element flags', async () => {
      const mockContent = `<?xml version="1.0" encoding="UTF-8"?>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
          <xs:complexType name="UserType">
            <xs:sequence>
              <xs:element name="name" type="xs:string"/>
            </xs:sequence>
          </xs:complexType>
          <xs:element name="User" type="UserType"/>
        </xs:schema>`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(mockContent);

      const result = await parser.parseXSD('/test/root.xsd');

      const userClass = result.find(cls => cls.name === 'User');
      expect(userClass).toBeDefined();
      expect(userClass!.isRootElement).toBe(true);
    });

    it('should handle array fields with maxOccurs', async () => {
      const mockContent = `<?xml version="1.0" encoding="UTF-8"?>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
          <xs:complexType name="CompanyType">
            <xs:sequence>
              <xs:element name="name" type="xs:string"/>
              <xs:element name="employees" type="xs:string" maxOccurs="unbounded"/>
            </xs:sequence>
          </xs:complexType>
        </xs:schema>`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(mockContent);

      const result = await parser.parseXSD('/test/array.xsd');

      const companyType = result.find(cls => cls.name === 'CompanyType');
      expect(companyType).toBeDefined();
      
      if (companyType) {
        const employeesField = companyType.fields.find(f => f.name === 'employees');
        expect(employeesField).toBeDefined();
        expect(employeesField!.isArray).toBe(true);
      }
    });

    it('should handle optional fields with minOccurs=0', async () => {
      const mockContent = `<?xml version="1.0" encoding="UTF-8"?>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
          <xs:complexType name="UserType">
            <xs:sequence>
              <xs:element name="name" type="xs:string"/>
              <xs:element name="email" type="xs:string" minOccurs="0"/>
            </xs:sequence>
          </xs:complexType>
        </xs:schema>`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(mockContent);

      const result = await parser.parseXSD('/test/optional.xsd');

      const userType = result.find(cls => cls.name === 'UserType');
      expect(userType).toBeDefined();
      
      if (userType) {
        const emailField = userType.fields.find(f => f.name === 'email');
        expect(emailField).toBeDefined();
        expect(emailField!.isOptional).toBe(true);
      }
    });
  });
});
