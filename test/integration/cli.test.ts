import { exec } from 'child_process';
import { promisify } from 'util';
import * as path from 'path';
import * as fs from 'fs';

const execAsync = promisify(exec);

describe('CLI Integration Tests', () => {
  const cliPath = path.join(__dirname, '../../dist/cli.js');
  const bwpFilePath = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp');
  const schemasPath = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas');
  const tempOutputDir = path.join(__dirname, '../temp-cli-output');
  const tempSpringBootProject = path.join(__dirname, '../temp-cli-spring-boot');

  beforeEach(() => {
    // 清理临时目录
    if (fs.existsSync(tempOutputDir)) {
      fs.rmSync(tempOutputDir, { recursive: true, force: true });
    }
    if (fs.existsSync(tempSpringBootProject)) {
      fs.rmSync(tempSpringBootProject, { recursive: true, force: true });
    }
  });

  afterEach(() => {
    // 清理临时目录
    if (fs.existsSync(tempOutputDir)) {
      fs.rmSync(tempOutputDir, { recursive: true, force: true });
    }
    if (fs.existsSync(tempSpringBootProject)) {
      fs.rmSync(tempSpringBootProject, { recursive: true, force: true });
    }
  });

  function createMockSpringBootProject() {
    fs.mkdirSync(tempSpringBootProject, { recursive: true });
    
    const pomContent = `<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.example</groupId>
    <artifactId>spring-boot-app</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.0</version>
        <relativePath/>
    </parent>
    
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
    
</project>`;

    fs.writeFileSync(path.join(tempSpringBootProject, 'pom.xml'), pomContent);
    fs.mkdirSync(path.join(tempSpringBootProject, 'src', 'main', 'java'), { recursive: true });
  }

  describe('validate command', () => {
    it('should validate a BWP file successfully', async () => {
      const { stdout, stderr } = await execAsync(`node ${cliPath} validate -i "${bwpFilePath}"`);
      
      expect(stderr).toBe('');
      expect(stdout).toContain('BWP file is valid!');
      expect(stdout).toContain('Process name: SearchMovies');
      expect(stdout).toContain('Namespace: http://xmlns.example.com/20190722212639');
    }, 30000);

    it('should fail validation for non-existent file', async () => {
      await expect(execAsync(`node ${cliPath} validate -i "/nonexistent/file.bwp"`))
        .rejects.toThrow();
    }, 10000);
  });

  describe('generate-models command', () => {
    it('should generate models from XSD schemas', async () => {
      const { stdout, stderr } = await execAsync(
        `node ${cliPath} generate-models -s "${schemasPath}" -o "${tempOutputDir}" -p "com.example.models"`
      );
      
      expect(stderr).toBe('');
      expect(stdout).toContain('Generated');
      expect(stdout).toContain('model classes');
      
      // 验证生成的文件
      const packageDir = path.join(tempOutputDir, 'com', 'example', 'models');
      expect(fs.existsSync(packageDir)).toBe(true);
      
      const javaFiles = fs.readdirSync(packageDir, { recursive: true })
        .filter(file => typeof file === 'string' && file.endsWith('.java'));
      expect(javaFiles.length).toBeGreaterThan(0);
    }, 60000);

    it('should fail for non-existent schemas directory', async () => {
      await expect(execAsync(`node ${cliPath} generate-models -s "/nonexistent/schemas" -o "${tempOutputDir}"`))
        .rejects.toThrow();
    }, 10000);
  });

  describe('convert command', () => {
    it('should convert BWP file to Java code', async () => {
      const { stdout, stderr } = await execAsync(
        `node ${cliPath} convert -i "${bwpFilePath}" -o "${tempOutputDir}" -p "com.example.movies"`
      );
      
      expect(stderr).toBe('');
      expect(stdout).toContain('Starting Tibco BW to Spring Boot conversion');
      expect(stdout).toContain('Successfully parsed BWP: SearchMovies');
      expect(stdout).toContain('Generating Java code from BWP');
      expect(stdout).toContain('Generated controller:');
      expect(stdout).toContain('Generated service:');
      expect(stdout).toContain('Conversion completed successfully!');
      
      // 验证生成的文件
      const packageDir = path.join(tempOutputDir, 'com', 'example', 'movies');
      expect(fs.existsSync(path.join(packageDir, 'SearchMoviesController.java'))).toBe(true);
      expect(fs.existsSync(path.join(packageDir, 'SearchMoviesService.java'))).toBe(true);
      
      // 验证生成的代码内容
      const controllerContent = fs.readFileSync(path.join(packageDir, 'SearchMoviesController.java'), 'utf-8');
      expect(controllerContent).toContain('@RestController');
      expect(controllerContent).toContain('SearchMoviesController');
      expect(controllerContent).toContain('@GetMapping');
    }, 60000);

    it('should convert with XSD schemas', async () => {
      const { stdout, stderr } = await execAsync(
        `node ${cliPath} convert -i "${bwpFilePath}" -s "${schemasPath}" -o "${tempOutputDir}" -p "com.example.movies"`
      );
      
      expect(stderr).toBe('');
      expect(stdout).toContain('Generating XSD models from:');
      expect(stdout).toContain('Generated');
      expect(stdout).toContain('model classes');
      
      // 验证生成了模型类
      const packageDir = path.join(tempOutputDir, 'com', 'example', 'movies');
      const allFiles = fs.readdirSync(packageDir, { recursive: true });
      const javaFiles = allFiles.filter(file => typeof file === 'string' && file.endsWith('.java'));
      
      // 应该有控制器、服务和模型类
      expect(javaFiles.length).toBeGreaterThan(2);
      expect(javaFiles.some(file => file.includes('Controller'))).toBe(true);
      expect(javaFiles.some(file => file.includes('Service'))).toBe(true);
    }, 60000);

    it('should convert and deploy to Spring Boot project', async () => {
      createMockSpringBootProject();
      
      const { stdout, stderr } = await execAsync(
        `node ${cliPath} convert -i "${bwpFilePath}" -o "${tempOutputDir}" -p "com.example.movies" --spring-boot-project "${tempSpringBootProject}"`
      );
      
      expect(stderr).toBe('');
      expect(stdout).toContain('Deploying to Spring Boot project:');
      expect(stdout).toContain('Deployment completed successfully');
      expect(stdout).toContain('Next steps:');
      expect(stdout).toContain('mvn spring-boot:run');
      
      // 验证文件被部署到 Spring Boot 项目
      const deployedPackageDir = path.join(tempSpringBootProject, 'src', 'main', 'java', 'com', 'example', 'movies');
      expect(fs.existsSync(path.join(deployedPackageDir, 'SearchMoviesController.java'))).toBe(true);
      expect(fs.existsSync(path.join(deployedPackageDir, 'SearchMoviesService.java'))).toBe(true);
      
      // 验证 pom.xml 被更新
      const pomContent = fs.readFileSync(path.join(tempSpringBootProject, 'pom.xml'), 'utf-8');
      expect(pomContent).toContain('spring-boot-starter-web');
      expect(pomContent).toContain('spring-boot-starter-validation');
      expect(pomContent).toContain('jackson-annotations');
    }, 60000);

    it('should handle custom options', async () => {
      const { stdout, stderr } = await execAsync(
        `node ${cliPath} convert -i "${bwpFilePath}" -o "${tempOutputDir}" -p "com.custom.package" --lombok --no-validation --no-jackson`
      );
      
      expect(stderr).toBe('');
      expect(stdout).toContain('Conversion completed successfully!');
      
      // 验证使用了自定义包名
      const packageDir = path.join(tempOutputDir, 'com', 'custom', 'package');
      expect(fs.existsSync(path.join(packageDir, 'SearchMoviesController.java'))).toBe(true);
    }, 60000);

    it('should fail for non-existent BWP file', async () => {
      await expect(execAsync(`node ${cliPath} convert -i "/nonexistent/file.bwp" -o "${tempOutputDir}"`))
        .rejects.toThrow();
    }, 10000);
  });

  describe('help and version', () => {
    it('should show help', async () => {
      const { stdout } = await execAsync(`node ${cliPath} --help`);
      
      expect(stdout).toContain('Convert Tibco BusinessWorks (.bwp) files to Spring Boot Java applications');
      expect(stdout).toContain('convert');
      expect(stdout).toContain('validate');
      expect(stdout).toContain('generate-models');
    }, 10000);

    it('should show version', async () => {
      const { stdout } = await execAsync(`node ${cliPath} --version`);
      
      expect(stdout.trim()).toBe('1.0.0');
    }, 10000);
  });
});
