import { XSDJavaModelGenerator } from '../../src/generators/xsd-java-model-generator';
import { JavaGenerationOptions } from '../../src/types';
import * as path from 'path';
import * as fs from 'fs';

describe('XSD Java Model Generation Tests', () => {
  let generator: XSDJavaModelGenerator;
  let options: JavaGenerationOptions;
  let tempOutputDir: string;

  beforeEach(() => {
    tempOutputDir = path.join(__dirname, '../temp-output');
    options = {
      packageName: 'com.example.movies.model',
      outputDir: tempOutputDir,
      useJSR303Validation: true,
      useLombok: false,
      useJacksonAnnotations: true,
      includeConstructors: true,
      includeToString: true
    };
    generator = new XSDJavaModelGenerator(options);

    // 确保输出目录存在
    if (!fs.existsSync(tempOutputDir)) {
      fs.mkdirSync(tempOutputDir, { recursive: true });
    }
  });

  afterEach(() => {
    // 清理临时文件
    if (fs.existsSync(tempOutputDir)) {
      fs.rmSync(tempOutputDir, { recursive: true, force: true });
    }
  });

  describe('Single XSD File Processing', () => {
    it('should generate Java classes from MovieCatalogMaster.xsd', async () => {
      const xsdFilePath = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas/MovieCatalogMaster.xsd');
      
      const classes = await generator.generateModelsFromFile(xsdFilePath);
      
      expect(classes.length).toBeGreaterThan(0);
      
      // 验证生成的类
      const omdbSearchElement = classes.find(cls => cls.name === 'OMDBSearchElementType' || cls.name === 'OMDBSearchElement');
      expect(omdbSearchElement).toBeDefined();
      expect(omdbSearchElement?.fields.length).toBeGreaterThan(0);
      
      // 验证字段
      const searchField = omdbSearchElement?.fields.find(field => field.name === 'search');
      expect(searchField).toBeDefined();
      expect(searchField?.isArray).toBe(true);
      
      console.log('Generated classes:', classes.map(c => c.name));
    });

    it('should generate Java classes from SearchMoviesParameterSchema.xsd', async () => {
      const xsdFilePath = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas/SearchMoviesParameterSchema.xsd');
      
      const classes = await generator.generateModelsFromFile(xsdFilePath);
      
      expect(classes.length).toBeGreaterThan(0);
      
      // 验证生成的类
      const moviesGetParameters = classes.find(cls => cls.name === 'MoviesGetParameters');
      expect(moviesGetParameters).toBeDefined();
      expect(moviesGetParameters?.isRootElement).toBe(true);
      
      // 验证字段
      const searchStringField = moviesGetParameters?.fields.find(field => field.name === 'searchString');
      expect(searchStringField).toBeDefined();
      expect(searchStringField?.javaType).toBe('String');
      expect(searchStringField?.isOptional).toBe(false);
      
      console.log('Generated classes:', classes.map(c => c.name));
    });
  });

  describe('Directory Processing', () => {
    it('should generate Java classes from all XSD files in schemas directory', async () => {
      const xsdDirectory = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas');
      
      const classes = await generator.generateModelsFromDirectory(xsdDirectory);
      
      expect(classes.length).toBeGreaterThan(0);
      
      // 验证包含了主要的类
      const classNames = classes.map(c => c.name);
      expect(classNames).toContain('OMDBSearchElementType');
      expect(classNames).toContain('MoviesGetParameters');
      
      console.log('All generated classes:', classNames);
    });
  });

  describe('BWP Process Model Generation', () => {
    it('should generate models for BWP process input/output types', async () => {
      const xsdDirectory = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas');
      
      const result = await generator.generateModelsForBWPProcess(
        'moviesGetParameters',
        'OMDBSearchElement',
        'http://xmlns.example.com/SearchMovies/parameters',
        'http://www.example.org/MovieCatalogMaster',
        xsdDirectory
      );
      
      expect(result.inputClass).toBeDefined();
      expect(result.outputClass).toBeDefined();
      
      expect(result.inputClass?.name).toMatch(/MoviesGetParameters/i);
      expect(result.outputClass?.name).toMatch(/OMDBSearchElement/i);
      
      console.log('Input class:', result.inputClass?.name);
      console.log('Output class:', result.outputClass?.name);
    });
  });

  describe('Java Code Generation Quality', () => {
    it('should generate valid Java code with proper annotations', async () => {
      const xsdFilePath = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas/SearchMoviesParameterSchema.xsd');
      
      const javaCodeStrings = await generator.generateJavaCodeString(xsdFilePath);
      
      expect(javaCodeStrings.length).toBeGreaterThan(0);
      
      const javaCode = javaCodeStrings[0];
      
      // 验证包声明
      expect(javaCode).toMatch(/package\s+com\.example\.movies\.model;/);
      
      // 验证导入
      expect(javaCode).toContain('import javax.validation.constraints.*;');
      expect(javaCode).toContain('import com.fasterxml.jackson.annotation.*;');
      
      // 验证类声明
      expect(javaCode).toMatch(/public\s+class\s+\w+/);
      
      // 验证注解
      expect(javaCode).toContain('@JsonIgnoreProperties(ignoreUnknown = true)');
      expect(javaCode).toContain('@JsonProperty');
      expect(javaCode).toContain('@NotNull');
      expect(javaCode).toContain('@NotBlank');
      
      // 验证字段声明
      expect(javaCode).toMatch(/private\s+String\s+\w+;/);
      
      // 验证构造函数
      expect(javaCode).toContain('Default constructor');
      expect(javaCode).toContain('Parameterized constructor');
      
      // 验证 getter/setter
      expect(javaCode).toMatch(/public\s+String\s+get\w+\(\)/);
      expect(javaCode).toMatch(/public\s+void\s+set\w+\(/);
      
      // 验证 toString
      expect(javaCode).toContain('@Override');
      expect(javaCode).toContain('public String toString()');
      
      console.log('Generated Java code sample:');
      console.log(javaCode.substring(0, 500) + '...');
    });
  });

  describe('Integration with BWP Process', () => {
    it('should provide complete model mapping for BWP process', async () => {
      const xsdDirectory = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas');
      
      const result = await generator.generateAllModelsForBWP(xsdDirectory);
      
      expect(result.allClasses.length).toBeGreaterThan(0);
      expect(result.inputOutputMapping.size).toBeGreaterThan(0);
      
      // 验证映射包含了 BWP 流程需要的类型
      expect(result.inputOutputMapping.has('MoviesGetParameters')).toBe(true);
      expect(result.inputOutputMapping.has('OMDBSearchElementType')).toBe(true);
      
      // 验证可以通过不同的键访问同一个类
      const inputClass1 = result.inputOutputMapping.get('MoviesGetParameters');
      const inputClass2 = result.inputOutputMapping.get('moviesgetparameters');
      expect(inputClass1).toBeDefined();
      expect(inputClass2).toBeDefined();
      
      console.log('Available type mappings:', Array.from(result.inputOutputMapping.keys()));
    });
  });
});
