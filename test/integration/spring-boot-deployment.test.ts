import { SpringBootDeployer } from '../../src/utils/spring-boot-deployer';
import { BWPParser } from '../../src/parsers/bwp-parser';
import { BWPJavaGenerator } from '../../src/generators/bwp-java-generator';
import { XSDJavaModelGenerator } from '../../src/generators/xsd-java-model-generator';
import { JavaGenerationOptions } from '../../src/types';
import * as path from 'path';
import * as fs from 'fs';

describe('Spring Boot Deployment Tests', () => {
  let deployer: SpringBootDeployer;
  let options: JavaGenerationOptions;
  let tempSpringBootProject: string;
  let tempGeneratedCode: string;

  beforeEach(() => {
    // 创建临时目录
    tempSpringBootProject = path.join(__dirname, '../temp-spring-boot');
    tempGeneratedCode = path.join(__dirname, '../temp-generated');

    options = {
      packageName: 'com.example.movies',
      outputDir: tempGeneratedCode,
      useJSR303Validation: true,
      useLombok: false,
      useJacksonAnnotations: true,
      includeConstructors: true,
      includeToString: true
    };

    deployer = new SpringBootDeployer(tempSpringBootProject, options);

    // 创建模拟的 Spring Boot 项目结构
    createMockSpringBootProject();
  });

  afterEach(() => {
    // 清理临时文件
    if (fs.existsSync(tempSpringBootProject)) {
      fs.rmSync(tempSpringBootProject, { recursive: true, force: true });
    }
    if (fs.existsSync(tempGeneratedCode)) {
      fs.rmSync(tempGeneratedCode, { recursive: true, force: true });
    }
  });

  function createMockSpringBootProject() {
    // 创建基本的 Spring Boot 项目结构
    fs.mkdirSync(tempSpringBootProject, { recursive: true });
    
    // 创建 pom.xml
    const pomContent = `<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.example</groupId>
    <artifactId>spring-boot-app</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.0</version>
        <relativePath/>
    </parent>
    
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
    
</project>`;

    fs.writeFileSync(path.join(tempSpringBootProject, 'pom.xml'), pomContent);
    
    // 创建 src/main/java 目录
    fs.mkdirSync(path.join(tempSpringBootProject, 'src', 'main', 'java'), { recursive: true });
  }

  function createMockGeneratedCode() {
    // 创建模拟的生成代码
    fs.mkdirSync(tempGeneratedCode, { recursive: true });
    
    const packageDir = path.join(tempGeneratedCode, 'com', 'example', 'movies');
    fs.mkdirSync(packageDir, { recursive: true });
    
    // 创建模拟的控制器
    const controllerContent = `package com.example.movies;

import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;

@RestController
public class SearchMoviesController {
    
    @Autowired
    private SearchMoviesService searchMoviesService;
    
    @GetMapping("/movies")
    public ResponseEntity<String> searchMovies(@RequestParam String query) {
        return ResponseEntity.ok("Movies found");
    }
}`;

    fs.writeFileSync(path.join(packageDir, 'SearchMoviesController.java'), controllerContent);
    
    // 创建模拟的服务
    const serviceContent = `package com.example.movies;

import org.springframework.stereotype.Service;

@Service
public class SearchMoviesService {
    
    public String searchMovies(String query) {
        return "Search results for: " + query;
    }
}`;

    fs.writeFileSync(path.join(packageDir, 'SearchMoviesService.java'), serviceContent);
    
    // 创建模型目录和文件
    const modelDir = path.join(packageDir, 'model');
    fs.mkdirSync(modelDir, { recursive: true });
    
    const modelContent = `package com.example.movies.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.NotNull;

public class MoviesGetParameters {
    
    @NotNull
    @JsonProperty("searchString")
    private String searchString;
    
    public String getSearchString() {
        return searchString;
    }
    
    public void setSearchString(String searchString) {
        this.searchString = searchString;
    }
}`;

    fs.writeFileSync(path.join(modelDir, 'MoviesGetParameters.java'), modelContent);
  }

  describe('Project Validation', () => {
    it('should validate a valid Spring Boot project', async () => {
      // 部署器应该能处理空的生成代码目录（只是警告，不抛出错误）
      await expect(deployer.deployGeneratedCode(tempGeneratedCode)).resolves.not.toThrow();

      // 创建生成的代码后应该成功
      createMockGeneratedCode();
      await expect(deployer.deployGeneratedCode(tempGeneratedCode)).resolves.not.toThrow();

      // 验证部署成功
      const isValid = await deployer.validateDeployment();
      expect(isValid).toBe(true);
    });

    it('should reject invalid project paths', async () => {
      const invalidDeployer = new SpringBootDeployer('/nonexistent/path', options);
      await expect(invalidDeployer.deployGeneratedCode(tempGeneratedCode)).rejects.toThrow('does not exist');
    });

    it('should get correct project info', () => {
      const projectInfo = deployer.getProjectInfo();
      expect(projectInfo.path).toBe(tempSpringBootProject);
      expect(projectInfo.type).toBe('maven');
    });
  });

  describe('File Deployment', () => {
    beforeEach(() => {
      createMockGeneratedCode();
    });

    it('should copy Java files to Spring Boot project', async () => {
      await deployer.deployGeneratedCode(tempGeneratedCode);
      
      // 验证文件是否被复制
      const targetPackageDir = path.join(tempSpringBootProject, 'src', 'main', 'java', 'com', 'example', 'movies');
      expect(fs.existsSync(path.join(targetPackageDir, 'SearchMoviesController.java'))).toBe(true);
      expect(fs.existsSync(path.join(targetPackageDir, 'SearchMoviesService.java'))).toBe(true);
      expect(fs.existsSync(path.join(targetPackageDir, 'model', 'MoviesGetParameters.java'))).toBe(true);
    });

    it('should validate deployment success', async () => {
      await deployer.deployGeneratedCode(tempGeneratedCode);
      const isValid = await deployer.validateDeployment();
      expect(isValid).toBe(true);
    });

    it('should update pom.xml with required dependencies', async () => {
      await deployer.deployGeneratedCode(tempGeneratedCode);
      
      const pomContent = fs.readFileSync(path.join(tempSpringBootProject, 'pom.xml'), 'utf-8');
      expect(pomContent).toContain('spring-boot-starter-web');
      expect(pomContent).toContain('spring-boot-starter-validation');
      expect(pomContent).toContain('jackson-annotations');
    });

    it('should create backup of pom.xml', async () => {
      await deployer.deployGeneratedCode(tempGeneratedCode);
      
      const backupPath = path.join(tempSpringBootProject, 'pom.xml.backup');
      expect(fs.existsSync(backupPath)).toBe(true);
    });
  });

  describe('End-to-End Integration', () => {
    it('should deploy complete BWP-generated project', async () => {
      // 解析真实的 BWP 文件
      const bwpFilePath = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp');
      const xsdDirectory = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas');
      
      // 解析 BWP
      const parser = new BWPParser();
      const parsedProcess = await parser.parseBWP(bwpFilePath);
      
      // 生成 XSD 模型
      const modelGenerator = new XSDJavaModelGenerator(options);
      await modelGenerator.generateAllModelsForBWP(xsdDirectory);
      
      // 生成 BWP Java 代码
      const bwpGenerator = new BWPJavaGenerator(options);
      const controllerCode = bwpGenerator.generateController(parsedProcess);
      const serviceCode = bwpGenerator.generateService(parsedProcess);
      
      // 写入生成的代码到临时目录
      const packageDir = path.join(tempGeneratedCode, 'com', 'example', 'movies');
      fs.mkdirSync(packageDir, { recursive: true });
      
      fs.writeFileSync(path.join(packageDir, 'SearchMoviesController.java'), controllerCode);
      fs.writeFileSync(path.join(packageDir, 'SearchMoviesService.java'), serviceCode);
      
      // 部署到 Spring Boot 项目
      await deployer.deployGeneratedCode(tempGeneratedCode);
      
      // 验证部署
      const isValid = await deployer.validateDeployment();
      expect(isValid).toBe(true);
      
      // 验证生成的文件内容
      const deployedController = fs.readFileSync(
        path.join(tempSpringBootProject, 'src', 'main', 'java', 'com', 'example', 'movies', 'SearchMoviesController.java'),
        'utf-8'
      );
      
      expect(deployedController).toContain('@RestController');
      expect(deployedController).toContain('SearchMoviesController');
      expect(deployedController).toContain('@GetMapping');
      
      console.log('End-to-end deployment test completed successfully');
    });
  });
});
