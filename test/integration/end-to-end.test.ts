import { BW<PERSON>arser } from '../../src/parsers/bwp-parser';
import { BWPJavaGenerator } from '../../src/generators/bwp-java-generator';
import { XSDJavaModelGenerator } from '../../src/generators/xsd-java-model-generator';
import { SpringBootDeployer } from '../../src/utils/spring-boot-deployer';
import { JavaGenerationOptions } from '../../src/types';
import * as path from 'path';
import * as fs from 'fs';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

describe('End-to-End Integration Tests', () => {
  let tempSpringBootProject: string;
  let tempGeneratedCode: string;
  let options: JavaGenerationOptions;

  beforeAll(() => {
    // 创建临时目录
    tempSpringBootProject = path.join(__dirname, '../temp-e2e-spring-boot');
    tempGeneratedCode = path.join(__dirname, '../temp-e2e-generated');

    options = {
      packageName: 'com.example.movies',
      outputDir: tempGeneratedCode,
      useJSR303Validation: true,
      useLombok: false,
      useJacksonAnnotations: true,
      includeConstructors: true,
      includeToString: true
    };

    // 清理并创建临时目录
    if (fs.existsSync(tempSpringBootProject)) {
      fs.rmSync(tempSpringBootProject, { recursive: true, force: true });
    }
    if (fs.existsSync(tempGeneratedCode)) {
      fs.rmSync(tempGeneratedCode, { recursive: true, force: true });
    }

    // 创建模拟的 Spring Boot 项目结构
    createMockSpringBootProject();
  });

  afterAll(() => {
    // 清理临时目录
    if (fs.existsSync(tempSpringBootProject)) {
      fs.rmSync(tempSpringBootProject, { recursive: true, force: true });
    }
    if (fs.existsSync(tempGeneratedCode)) {
      fs.rmSync(tempGeneratedCode, { recursive: true, force: true });
    }
  });

  function createMockSpringBootProject() {
    // 创建基本的 Spring Boot 项目结构
    const srcMainJava = path.join(tempSpringBootProject, 'src', 'main', 'java');
    const srcMainResources = path.join(tempSpringBootProject, 'src', 'main', 'resources');
    const srcTestJava = path.join(tempSpringBootProject, 'src', 'test', 'java');

    fs.mkdirSync(srcMainJava, { recursive: true });
    fs.mkdirSync(srcMainResources, { recursive: true });
    fs.mkdirSync(srcTestJava, { recursive: true });

    // 创建 pom.xml
    const pomContent = `<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version>
        <relativePath/>
    </parent>
    <groupId>com.example</groupId>
    <artifactId>demo</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>demo</name>
    <description>Demo project for Spring Boot</description>
    <properties>
        <java.version>17</java.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>`;

    fs.writeFileSync(path.join(tempSpringBootProject, 'pom.xml'), pomContent);

    // 创建主应用类
    const appContent = `package com.example;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class DemoApplication {
    public static void main(String[] args) {
        SpringApplication.run(DemoApplication.class, args);
    }
}`;

    const appDir = path.join(srcMainJava, 'com', 'example');
    fs.mkdirSync(appDir, { recursive: true });
    fs.writeFileSync(path.join(appDir, 'DemoApplication.java'), appContent);

    // 创建 application.properties
    const appPropsContent = `server.port=8080
logging.level.com.example=DEBUG`;
    fs.writeFileSync(path.join(srcMainResources, 'application.properties'), appPropsContent);
  }

  describe('Complete BWP to Spring Boot Workflow', () => {
    it('should complete the entire workflow from BWP parsing to API validation', async () => {
      // 1. 解析 BWP 文件
      console.log('Step 1: Parsing BWP file...');
      const bwpFilePath = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp');
      const xsdDirectory = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas');
      const swaggerJsonPath = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Resources/swagger.json');

      expect(fs.existsSync(bwpFilePath)).toBe(true);
      expect(fs.existsSync(xsdDirectory)).toBe(true);
      expect(fs.existsSync(swaggerJsonPath)).toBe(true);

      const parser = new BWPParser();
      const parsedProcess = await parser.parseBWP(bwpFilePath);

      expect(parsedProcess).toBeDefined();
      expect(parsedProcess.name).toBe('SearchMovies');
      expect(parsedProcess.restEndpoints.length).toBeGreaterThan(0);

      // 2. 生成 XSD 模型
      console.log('Step 2: Generating XSD models...');
      const modelGenerator = new XSDJavaModelGenerator(options);
      const modelResult = await modelGenerator.generateAllModelsForBWP(xsdDirectory);

      expect(modelResult.allClasses.length).toBeGreaterThan(0);
      expect(fs.existsSync(tempGeneratedCode)).toBe(true);

      // 3. 生成 BWP Java 代码
      console.log('Step 3: Generating BWP Java code...');
      const bwpGenerator = new BWPJavaGenerator(options);
      const controllerCode = bwpGenerator.generateController(parsedProcess);
      const serviceCode = bwpGenerator.generateService(parsedProcess);

      expect(controllerCode).toContain('@RestController');
      expect(controllerCode).toContain('SearchMoviesController');
      expect(serviceCode).toContain('@Service');
      expect(serviceCode).toContain('SearchMoviesService');

      // 写入生成的代码
      const packageDir = path.join(tempGeneratedCode, 'com', 'example', 'movies');
      fs.mkdirSync(packageDir, { recursive: true });
      fs.writeFileSync(path.join(packageDir, 'SearchMoviesController.java'), controllerCode);
      fs.writeFileSync(path.join(packageDir, 'SearchMoviesService.java'), serviceCode);

      // 4. 部署到 Spring Boot 项目
      console.log('Step 4: Deploying to Spring Boot project...');
      const deployer = new SpringBootDeployer(tempSpringBootProject, options);
      await deployer.deployGeneratedCode(tempGeneratedCode);

      // 验证部署
      const isValid = await deployer.validateDeployment();
      expect(isValid).toBe(true);

      // 5. 验证 API 一致性
      console.log('Step 5: Validating API consistency...');
      const apiValidation = await deployer.validateApiConsistency(swaggerJsonPath);
      expect(apiValidation.isConsistent).toBe(true);
      expect(apiValidation.differences.length).toBe(0);

      // 6. 验证生成的文件内容
      console.log('Step 6: Verifying generated files...');
      const deployedController = path.join(tempSpringBootProject, 'src', 'main', 'java', 'com', 'example', 'movies', 'SearchMoviesController.java');
      const deployedService = path.join(tempSpringBootProject, 'src', 'main', 'java', 'com', 'example', 'movies', 'SearchMoviesService.java');

      expect(fs.existsSync(deployedController)).toBe(true);
      expect(fs.existsSync(deployedService)).toBe(true);

      const controllerContent = fs.readFileSync(deployedController, 'utf-8');
      expect(controllerContent).toContain('@RestController');
      expect(controllerContent).toContain('@GetMapping("/movies")');
      expect(controllerContent).toContain('@RequestParam("searchString")');
      expect(controllerContent).toContain('ResponseEntity<OMDBSearchElement>');

      console.log('✅ End-to-end workflow completed successfully!');
    }, 60000); // 60 second timeout

    it('should handle toString method generation correctly', async () => {
      // 验证生成的模型类有正确的 toString 方法
      const modelFiles = fs.readdirSync(path.join(tempSpringBootProject, 'src', 'main', 'java', 'com', 'example', 'movies'))
        .filter(file => file.endsWith('.java') && !file.includes('Controller') && !file.includes('Service'));

      expect(modelFiles.length).toBeGreaterThan(0);

      for (const modelFile of modelFiles.slice(0, 3)) { // 检查前3个模型文件
        const modelPath = path.join(tempSpringBootProject, 'src', 'main', 'java', 'com', 'example', 'movies', modelFile);
        const content = fs.readFileSync(modelPath, 'utf-8');
        
        // 验证有 toString 方法
        expect(content).toContain('toString()');
        expect(content).toContain('return ');
        
        // 验证 toString 方法不是空的
        expect(content).not.toMatch(/toString\(\)\s*\{\s*return\s*"[^"]*\{\s*}"\s*;\s*\}/);
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle missing BWP file gracefully', async () => {
      const parser = new BWPParser();
      await expect(parser.parseBWP('/nonexistent/file.bwp')).rejects.toThrow();
    });

    it('should handle invalid Spring Boot project path', async () => {
      const deployer = new SpringBootDeployer('/nonexistent/path', options);
      await expect(deployer.deployGeneratedCode(tempGeneratedCode)).rejects.toThrow();
    });

    it('should handle missing swagger.json file', async () => {
      const deployer = new SpringBootDeployer(tempSpringBootProject, options);
      const result = await deployer.validateApiConsistency('/nonexistent/swagger.json');
      expect(result.isConsistent).toBe(false);
      expect(result.differences.length).toBeGreaterThan(0);
    });
  });
});
