import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import path from 'path';
import fs from 'fs';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

describe('End-to-End CLI Automation Tests', () => {
  const testOutputDir = path.join(__dirname, '../temp-e2e-output');
  const springBootTestDir = path.join(__dirname, '../temp-e2e-spring-boot');
  const testBWPFile = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp');

  beforeAll(() => {
    // 确保测试目录存在
    if (!fs.existsSync(testOutputDir)) {
      fs.mkdirSync(testOutputDir, { recursive: true });
    }
    if (!fs.existsSync(springBootTestDir)) {
      fs.mkdirSync(springBootTestDir, { recursive: true });
    }
  });

  afterAll(() => {
    // 清理测试目录
    if (fs.existsSync(testOutputDir)) {
      fs.rmSync(testOutputDir, { recursive: true, force: true });
    }
    if (fs.existsSync(springBootTestDir)) {
      fs.rmSync(springBootTestDir, { recursive: true, force: true });
    }
  });

  describe('Complete CLI Workflow', () => {
    it('should execute complete BWP to Spring Boot conversion workflow', async () => {
      // 1. 验证 BWP 文件存在
      expect(fs.existsSync(testBWPFile)).toBe(true);

      // 2. 执行 CLI quick 命令
      const cliCommand = `node dist/cli.js quick "${testBWPFile}"`;
      console.log('Executing CLI command:', cliCommand);
      
      const { stdout, stderr } = await execAsync(cliCommand, {
        cwd: path.join(__dirname, '../..'),
        timeout: 60000 // 60 seconds timeout
      });

      console.log('CLI stdout:', stdout);
      if (stderr) {
        console.log('CLI stderr:', stderr);
      }

      // 3. 验证代码生成成功
      expect(stdout).toContain('✅ Successfully parsed BWP:');
      expect(stdout).toContain('✅ Generated');
      expect(stdout).toContain('model classes');
      expect(stdout).toContain('✅ Generated controller:');
      expect(stdout).toContain('✅ Generated service:');
      expect(stdout).toContain('✅ Deployment completed successfully');

      // 4. 验证生成的文件存在
      const springBootProjectDir = path.join(__dirname, '../../spring-boilerplate');
      const controllerFile = path.join(springBootProjectDir, 'src/main/java/com/example/movies/SearchMoviesController.java');
      const serviceFile = path.join(springBootProjectDir, 'src/main/java/com/example/movies/SearchMoviesService.java');
      
      expect(fs.existsSync(controllerFile)).toBe(true);
      expect(fs.existsSync(serviceFile)).toBe(true);

      // 5. 验证生成的代码质量
      const controllerContent = fs.readFileSync(controllerFile, 'utf-8');
      const serviceContent = fs.readFileSync(serviceFile, 'utf-8');

      // Controller 验证
      expect(controllerContent).toContain('@RestController');
      expect(controllerContent).toContain('@GetMapping("/movies")');
      expect(controllerContent).toContain('SearchMoviesService');
      expect(controllerContent).toContain('ResponseEntity<OMDBSearchElement>');

      // Service 验证
      expect(serviceContent).toContain('@Service');
      expect(serviceContent).toContain('RestTemplate');
      expect(serviceContent).toContain('OMDBSearchElement');
      expect(serviceContent).toContain('restTemplate.getForObject');

      console.log('✅ Code generation and deployment validation passed');
    }, 120000); // 2 minutes timeout

    it('should compile and start Spring Boot application', async () => {
      const springBootProjectDir = path.join(__dirname, '../../spring-boilerplate');
      
      // 1. 编译项目
      console.log('Compiling Spring Boot project...');
      const { stdout: compileStdout, stderr: compileStderr } = await execAsync('mvn clean compile', {
        cwd: springBootProjectDir,
        timeout: 120000 // 2 minutes timeout
      });

      console.log('Compile stdout:', compileStdout);
      if (compileStderr) {
        console.log('Compile stderr:', compileStderr);
      }

      expect(compileStdout).toContain('BUILD SUCCESS');

      // 2. 运行测试（如果有的话）
      try {
        const { stdout: testStdout } = await execAsync('mvn test', {
          cwd: springBootProjectDir,
          timeout: 60000 // 1 minute timeout
        });
        console.log('Test stdout:', testStdout);
      } catch (error) {
        // 测试失败不影响主要流程，只记录日志
        console.log('Tests failed or no tests found, continuing...');
      }

      console.log('✅ Spring Boot compilation successful');
    }, 180000); // 3 minutes timeout

    it('should validate API endpoints functionality', async () => {
      // 这个测试需要应用运行，我们可以通过检查生成的代码来验证 API 结构
      const springBootProjectDir = path.join(__dirname, '../../spring-boilerplate');
      const controllerFile = path.join(springBootProjectDir, 'src/main/java/com/example/movies/SearchMoviesController.java');
      
      expect(fs.existsSync(controllerFile)).toBe(true);
      
      const controllerContent = fs.readFileSync(controllerFile, 'utf-8');
      
      // 验证 API 端点定义
      expect(controllerContent).toMatch(/@GetMapping\("\/movies"\)/);
      expect(controllerContent).toMatch(/@RequestParam\("searchString"\)\s+String\s+searchString/);
      expect(controllerContent).toMatch(/ResponseEntity<OMDBSearchElement>/);
      
      // 验证错误处理
      expect(controllerContent).toContain('try {');
      expect(controllerContent).toContain('} catch (IllegalArgumentException e) {');
      expect(controllerContent).toContain('} catch (Exception e) {');
      expect(controllerContent).toContain('ResponseEntity.badRequest()');
      expect(controllerContent).toContain('ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)');

      console.log('✅ API endpoints validation passed');
    });

    it('should validate generated model classes', async () => {
      const springBootProjectDir = path.join(__dirname, '../../spring-boilerplate');
      const modelsDir = path.join(springBootProjectDir, 'src/main/java/com/example/movies');
      
      // 验证关键模型类存在
      const expectedModels = [
        'OMDBSearchElement.java',
        'SearchType.java',
        'MoviesGetParameters.java'
      ];

      for (const modelFile of expectedModels) {
        const modelPath = path.join(modelsDir, modelFile);
        expect(fs.existsSync(modelPath)).toBe(true);
        
        const modelContent = fs.readFileSync(modelPath, 'utf-8');
        
        // 验证基本 Java 类结构
        expect(modelContent).toMatch(/package\s+com\.example\.movies;/);
        expect(modelContent).toMatch(/public\s+class\s+\w+/);
        
        // 验证 toString 方法存在（根据用户要求）
        expect(modelContent).toContain('toString()');
      }

      console.log('✅ Model classes validation passed');
    });
  });

  describe('CLI Command Validation', () => {
    it('should show help when no arguments provided', async () => {
      const { stdout } = await execAsync('node dist/cli.js --help', {
        cwd: path.join(__dirname, '../..'),
        timeout: 10000
      });

      expect(stdout).toContain('Usage:');
      expect(stdout).toContain('Commands:');
      expect(stdout).toContain('quick');
    });

    it('should handle invalid BWP file gracefully', async () => {
      const invalidFile = '/nonexistent/file.bwp';
      
      try {
        await execAsync(`node dist/cli.js quick "${invalidFile}"`, {
          cwd: path.join(__dirname, '../..'),
          timeout: 10000
        });
        // 如果没有抛出错误，测试失败
        expect(true).toBe(false);
      } catch (error: any) {
        expect(error.code).toBe(1);
        expect(error.stderr || error.stdout).toContain('Error');
      }
    });
  });
});
